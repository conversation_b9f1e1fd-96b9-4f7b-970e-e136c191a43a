#!/bin/bash

# InsureAI Deployment Script
# Usage: ./deploy.sh [development|production]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default environment
ENVIRONMENT=${1:-development}

echo -e "${GREEN}🚀 Starting InsureAI deployment for ${ENVIRONMENT} environment${NC}"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️  .env file not found. Creating from .env.example${NC}"
    if [ -f .env.example ]; then
        cp .env.example .env
        echo -e "${YELLOW}📝 Please edit .env file with your actual values before continuing.${NC}"
        read -p "Press enter to continue after editing .env file..."
    else
        echo -e "${RED}❌ .env.example file not found. Please create .env file manually.${NC}"
        exit 1
    fi
fi

# Function to deploy development environment
deploy_development() {
    echo -e "${GREEN}🔧 Deploying development environment...${NC}"
    
    # Stop existing containers
    docker-compose down
    
    # Build and start containers
    docker-compose up --build -d
    
    echo -e "${GREEN}✅ Development deployment complete!${NC}"
    echo -e "${GREEN}🌐 Frontend: http://localhost:3000${NC}"
    echo -e "${GREEN}🔧 Backend API: http://localhost:8000${NC}"
    echo -e "${GREEN}📚 API Docs: http://localhost:8000/docs${NC}"
}

# Function to deploy production environment
deploy_production() {
    echo -e "${GREEN}🔧 Deploying production environment...${NC}"
    
    # Check if production env file exists
    if [ ! -f .env.production ]; then
        echo -e "${YELLOW}⚠️  .env.production file not found. Using .env${NC}"
    fi
    
    # Stop existing containers
    docker-compose -f docker-compose.prod.yml down
    
    # Build and start containers
    if [ -f .env.production ]; then
        docker-compose -f docker-compose.prod.yml --env-file .env.production up --build -d
    else
        docker-compose -f docker-compose.prod.yml up --build -d
    fi
    
    echo -e "${GREEN}✅ Production deployment complete!${NC}"
    echo -e "${GREEN}🌐 Application: http://localhost${NC}"
    echo -e "${GREEN}🔧 Backend API: http://localhost:8000${NC}"
}

# Function to show logs
show_logs() {
    echo -e "${GREEN}📋 Showing application logs...${NC}"
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.prod.yml logs -f
    else
        docker-compose logs -f
    fi
}

# Function to stop services
stop_services() {
    echo -e "${YELLOW}🛑 Stopping services...${NC}"
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.prod.yml down
    else
        docker-compose down
    fi
    echo -e "${GREEN}✅ Services stopped${NC}"
}

# Function to show status
show_status() {
    echo -e "${GREEN}📊 Service status:${NC}"
    if [ "$ENVIRONMENT" = "production" ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
}

# Main deployment logic
case $ENVIRONMENT in
    development|dev)
        deploy_development
        ;;
    production|prod)
        deploy_production
        ;;
    logs)
        show_logs
        ;;
    stop)
        stop_services
        ;;
    status)
        show_status
        ;;
    *)
        echo -e "${RED}❌ Invalid environment. Use: development, production, logs, stop, or status${NC}"
        echo "Usage: $0 [development|production|logs|stop|status]"
        exit 1
        ;;
esac

# Show final status if deploying
if [ "$ENVIRONMENT" = "development" ] || [ "$ENVIRONMENT" = "dev" ] || [ "$ENVIRONMENT" = "production" ] || [ "$ENVIRONMENT" = "prod" ]; then
    echo ""
    echo -e "${GREEN}📊 Final service status:${NC}"
    show_status
    
    echo ""
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo -e "${YELLOW}💡 Useful commands:${NC}"
    echo "  View logs: ./deploy.sh logs"
    echo "  Stop services: ./deploy.sh stop"
    echo "  Check status: ./deploy.sh status"
fi
