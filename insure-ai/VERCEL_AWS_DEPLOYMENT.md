# Vercel + AWS Deployment Guide

This guide will help you deploy your InsureAI application with **Vercel hosting the frontend** and **AWS ECS/Fargate hosting the backend**.

## 🎯 Architecture Overview

- **Frontend**: Next.js app deployed on Vercel
- **Backend**: FastAPI app running on AWS ECS Fargate
- **Storage**: AWS EFS for persistent file storage
- **Load Balancer**: AWS Application Load Balancer
- **Container Registry**: AWS ECR
- **AI Services**: AWS Bedrock for document processing

## 📋 Prerequisites

### AWS Requirements
- AWS Account with appropriate permissions
- AWS CLI installed and configured
- Docker installed locally
- Domain name (optional, for custom domain)

### Vercel Requirements
- Vercel account (free tier available)
- GitHub repository with your code

## 🚀 Step-by-Step Deployment

### Step 1: Prepare AWS Environment

1. **Install AWS CLI** (if not already installed):
   ```bash
   # macOS
   brew install awscli
   
   # Windows
   # Download from https://aws.amazon.com/cli/
   
   # Linux
   curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
   unzip awscliv2.zip
   sudo ./aws/install
   ```

2. **Configure AWS CLI**:
   ```bash
   aws configure
   # Enter your AWS Access Key ID
   # Enter your AWS Secret Access Key
   # Enter your default region (e.g., us-east-1)
   # Enter output format (json)
   ```

3. **Verify AWS Configuration**:
   ```bash
   aws sts get-caller-identity
   ```

### Step 2: Deploy Backend to AWS

1. **Make the deployment script executable**:
   ```bash
   chmod +x aws/deploy-to-aws.sh
   ```

2. **Run the AWS deployment script**:
   ```bash
   # Basic deployment (uses default VPC)
   ./aws/deploy-to-aws.sh insure-ai-stack us-east-1
   
   # With custom domain (requires ACM certificate)
   ./aws/deploy-to-aws.sh insure-ai-stack us-east-1 api.yourdomain.com arn:aws:acm:us-east-1:123456789012:certificate/your-cert-id
   ```

3. **Wait for deployment** (takes 10-15 minutes):
   - CloudFormation stack creation
   - ECR repository setup
   - Docker image build and push
   - ECS service deployment

4. **Note the backend URL** from the script output:
   ```
   Backend URL: http://your-alb-dns-name.us-east-1.elb.amazonaws.com
   ```

### Step 3: Deploy Frontend to Vercel

1. **Push your code to GitHub** (if not already done):
   ```bash
   git add .
   git commit -m "Add deployment configurations"
   git push origin main
   ```

2. **Deploy to Vercel**:
   
   **Option A: Using Vercel CLI**
   ```bash
   # Install Vercel CLI
   npm install -g vercel
   
   # Login to Vercel
   vercel login
   
   # Deploy from frontend directory
   cd frontend
   vercel --prod
   ```
   
   **Option B: Using Vercel Dashboard**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Set **Root Directory** to `frontend`
   - Add environment variables (see below)
   - Click "Deploy"

3. **Configure Environment Variables in Vercel**:
   - Go to your project settings in Vercel
   - Navigate to "Environment Variables"
   - Add the following:
     ```
     NEXT_PUBLIC_API_URL = http://your-alb-dns-name.us-east-1.elb.amazonaws.com
     ```

4. **Redeploy** after adding environment variables:
   ```bash
   vercel --prod
   ```

### Step 4: Configure CORS and Security

1. **Update backend CORS settings**:
   - Go to AWS Secrets Manager
   - Find the secret `insure-ai/allowed-origins`
   - Update the value to include your Vercel domain:
     ```json
     {
       "allowed_origins": "https://your-vercel-app.vercel.app,https://yourdomain.com"
     }
     ```

2. **Restart ECS service** to pick up new settings:
   ```bash
   aws ecs update-service \
     --cluster insure-ai-cluster \
     --service insure-ai-backend-service \
     --force-new-deployment \
     --region us-east-1
   ```

### Step 5: Set Up Custom Domain (Optional)

#### For Backend (AWS):
1. **Get an SSL certificate** from AWS Certificate Manager
2. **Update CloudFormation stack** with certificate ARN
3. **Create DNS records**:
   - CNAME: `api.yourdomain.com` → `your-alb-dns-name.us-east-1.elb.amazonaws.com`

#### For Frontend (Vercel):
1. **Add domain in Vercel dashboard**:
   - Go to project settings → Domains
   - Add your domain (e.g., `yourdomain.com`)
2. **Configure DNS**:
   - Add CNAME record: `www.yourdomain.com` → `cname.vercel-dns.com`
   - Add A record: `yourdomain.com` → `76.76.19.61`

## 🔧 Environment Variables Reference

### AWS Backend (via Secrets Manager):
```bash
# Required secrets in AWS Secrets Manager:
insure-ai/secret-key          # Auto-generated secure key
insure-ai/allowed-origins     # Your frontend domains
```

### Vercel Frontend:
```bash
NEXT_PUBLIC_API_URL=https://api.yourdomain.com  # Your backend URL
```

## 📊 Monitoring and Maintenance

### AWS CloudWatch Logs:
```bash
# View backend logs
aws logs tail /ecs/insure-ai-backend --follow --region us-east-1
```

### ECS Service Management:
```bash
# Check service status
aws ecs describe-services \
  --cluster insure-ai-cluster \
  --services insure-ai-backend-service \
  --region us-east-1

# Scale service
aws ecs update-service \
  --cluster insure-ai-cluster \
  --service insure-ai-backend-service \
  --desired-count 2 \
  --region us-east-1
```

### Vercel Deployment:
```bash
# View deployment logs
vercel logs your-deployment-url

# Redeploy
vercel --prod
```

## 💰 Cost Estimation

### AWS Costs (Monthly):
- **ECS Fargate**: ~$15-30 (1 task, 0.5 vCPU, 1GB RAM)
- **Application Load Balancer**: ~$16
- **EFS Storage**: ~$0.30/GB
- **Data Transfer**: Variable based on usage
- **Total**: ~$35-50/month for small-medium usage

### Vercel Costs:
- **Hobby Plan**: Free (includes custom domains)
- **Pro Plan**: $20/month (for teams/commercial use)

## 🔒 Security Best Practices

1. **Enable AWS CloudTrail** for audit logging
2. **Use IAM roles** with minimal required permissions
3. **Enable VPC Flow Logs** for network monitoring
4. **Regular security updates** for container images
5. **Use AWS Secrets Manager** for sensitive configuration
6. **Enable HTTPS** with valid SSL certificates

## 🚨 Troubleshooting

### Common Issues:

**Backend not accessible:**
- Check ECS service status
- Verify security group rules
- Check target group health

**CORS errors:**
- Verify allowed origins in Secrets Manager
- Ensure frontend URL is correctly configured

**Deployment failures:**
- Check CloudFormation events
- Verify AWS permissions
- Check Docker image build logs

**Frontend not connecting to backend:**
- Verify `NEXT_PUBLIC_API_URL` environment variable
- Check network connectivity
- Verify backend health endpoint

## 🔄 Updates and Redeployment

### Backend Updates:
```bash
# Rebuild and push new image
./aws/deploy-to-aws.sh insure-ai-stack us-east-1

# Or just update the service
aws ecs update-service \
  --cluster insure-ai-cluster \
  --service insure-ai-backend-service \
  --force-new-deployment \
  --region us-east-1
```

### Frontend Updates:
```bash
# Automatic deployment on git push (if connected to GitHub)
git push origin main

# Manual deployment
vercel --prod
```

## 🎉 Success!

After completing these steps, you'll have:
- ✅ Scalable backend running on AWS ECS Fargate
- ✅ Fast frontend deployed on Vercel's global CDN
- ✅ Secure HTTPS connections
- ✅ Persistent file storage with AWS EFS
- ✅ Production-ready monitoring and logging

Your application will be accessible at:
- **Frontend**: `https://your-vercel-app.vercel.app`
- **Backend API**: `https://api.yourdomain.com` (or ALB DNS)
- **API Documentation**: `https://api.yourdomain.com/docs`
