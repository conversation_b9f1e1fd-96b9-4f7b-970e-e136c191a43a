{"version": 2, "name": "insure-ai-frontend", "buildCommand": "cd frontend && npm run build", "outputDirectory": "frontend/.next", "installCommand": "cd frontend && npm install", "framework": "nextjs", "functions": {"frontend/src/app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}