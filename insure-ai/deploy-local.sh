#!/bin/bash

# InsureAI Local Development Deployment Script
# Usage: ./deploy-local.sh [start|stop|restart|logs|clean]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default action
ACTION=${1:-start}

echo -e "${GREEN}🏠 InsureAI Local Development Manager${NC}"

# Function to start local development
start_local() {
    echo -e "${GREEN}🚀 Starting local development environment...${NC}"
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
        exit 1
    fi
    
    # Create local environment file if it doesn't exist
    if [ ! -f backend/.env ]; then
        echo -e "${YELLOW}📝 Creating local environment file...${NC}"
        cp backend/.env.local backend/.env
    fi
    
    # Stop any existing containers
    docker-compose -f docker-compose.local.yml down 2>/dev/null || true
    
    # Build and start containers
    echo -e "${BLUE}🔨 Building and starting containers...${NC}"
    docker-compose -f docker-compose.local.yml up --build -d
    
    # Wait for services to be ready
    echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
    sleep 10
    
    # Check health
    check_health
    
    echo -e "${GREEN}✅ Local development environment started successfully!${NC}"
    show_urls
}

# Function to stop local development
stop_local() {
    echo -e "${YELLOW}🛑 Stopping local development environment...${NC}"
    docker-compose -f docker-compose.local.yml down
    echo -e "${GREEN}✅ Local development environment stopped${NC}"
}

# Function to restart local development
restart_local() {
    echo -e "${YELLOW}🔄 Restarting local development environment...${NC}"
    stop_local
    start_local
}

# Function to show logs
show_logs() {
    echo -e "${BLUE}📋 Showing logs...${NC}"
    docker-compose -f docker-compose.local.yml logs -f
}

# Function to clean up everything
clean_local() {
    echo -e "${YELLOW}🧹 Cleaning up local development environment...${NC}"
    
    # Stop containers
    docker-compose -f docker-compose.local.yml down
    
    # Remove volumes
    docker-compose -f docker-compose.local.yml down -v
    
    # Remove images
    docker-compose -f docker-compose.local.yml down --rmi all
    
    # Remove local data files
    read -p "Do you want to delete local database and storage? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -f backend/local_data.db
        rm -rf backend/local_storage/
        echo -e "${GREEN}✅ Local data cleaned${NC}"
    fi
    
    echo -e "${GREEN}✅ Local development environment cleaned${NC}"
}

# Function to check health
check_health() {
    echo -e "${BLUE}🔍 Checking service health...${NC}"
    
    # Check backend health
    for i in {1..30}; do
        if curl -s http://localhost:8000/health > /dev/null; then
            echo -e "${GREEN}✅ Backend is healthy${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ Backend health check failed${NC}"
            return 1
        fi
        sleep 2
    done
    
    # Check frontend
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null; then
            echo -e "${GREEN}✅ Frontend is healthy${NC}"
            break
        fi
        if [ $i -eq 30 ]; then
            echo -e "${RED}❌ Frontend health check failed${NC}"
            return 1
        fi
        sleep 2
    done
}

# Function to show URLs and info
show_urls() {
    echo ""
    echo -e "${GREEN}🌐 Your InsureAI application is running locally!${NC}"
    echo ""
    echo -e "${BLUE}📱 Frontend:${NC} http://localhost:3000"
    echo -e "${BLUE}🔧 Backend API:${NC} http://localhost:8000"
    echo -e "${BLUE}📚 API Documentation:${NC} http://localhost:8000/docs"
    echo -e "${BLUE}🔍 API Health Check:${NC} http://localhost:8000/health"
    echo ""
    echo -e "${YELLOW}👤 Default Users:${NC}"
    echo "  Admin: admin / admin123"
    echo "  User:  user1 / user123"
    echo "  Demo:  demo  / demo123"
    echo ""
    echo -e "${YELLOW}💾 Local Data:${NC}"
    echo "  Database: backend/local_data.db"
    echo "  Files:    backend/local_storage/"
    echo ""
    echo -e "${YELLOW}🛠 Useful Commands:${NC}"
    echo "  View logs:    ./deploy-local.sh logs"
    echo "  Restart:      ./deploy-local.sh restart"
    echo "  Stop:         ./deploy-local.sh stop"
    echo "  Clean all:    ./deploy-local.sh clean"
}

# Function to show status
show_status() {
    echo -e "${BLUE}📊 Service Status:${NC}"
    docker-compose -f docker-compose.local.yml ps
    
    echo ""
    echo -e "${BLUE}💾 Local Data Status:${NC}"
    if [ -f backend/local_data.db ]; then
        echo -e "${GREEN}✅ Database file exists${NC}"
        echo "   Size: $(du -h backend/local_data.db | cut -f1)"
    else
        echo -e "${YELLOW}⚠️  Database file not found${NC}"
    fi
    
    if [ -d backend/local_storage ]; then
        echo -e "${GREEN}✅ Storage directory exists${NC}"
        echo "   Files: $(find backend/local_storage -type f | wc -l)"
        echo "   Size: $(du -sh backend/local_storage | cut -f1)"
    else
        echo -e "${YELLOW}⚠️  Storage directory not found${NC}"
    fi
}

# Function to open browser
open_browser() {
    echo -e "${BLUE}🌐 Opening browser...${NC}"
    
    # Detect OS and open browser
    case "$(uname -s)" in
        Darwin)  # macOS
            open http://localhost:3000
            ;;
        Linux)
            xdg-open http://localhost:3000
            ;;
        CYGWIN*|MINGW32*|MSYS*|MINGW*)  # Windows
            start http://localhost:3000
            ;;
        *)
            echo -e "${YELLOW}Please open http://localhost:3000 in your browser${NC}"
            ;;
    esac
}

# Main logic
case $ACTION in
    start)
        start_local
        ;;
    stop)
        stop_local
        ;;
    restart)
        restart_local
        ;;
    logs)
        show_logs
        ;;
    clean)
        clean_local
        ;;
    status)
        show_status
        ;;
    open)
        open_browser
        ;;
    *)
        echo -e "${RED}❌ Invalid action. Use: start, stop, restart, logs, clean, status, or open${NC}"
        echo ""
        echo "Usage: $0 [start|stop|restart|logs|clean|status|open]"
        echo ""
        echo "Commands:"
        echo "  start   - Start local development environment"
        echo "  stop    - Stop local development environment"
        echo "  restart - Restart local development environment"
        echo "  logs    - Show container logs"
        echo "  clean   - Clean up everything (containers, volumes, data)"
        echo "  status  - Show service and data status"
        echo "  open    - Open application in browser"
        exit 1
        ;;
esac
