version: '3.8'

services:
  backend-local:
    build:
      context: ./backend
      dockerfile: Dockerfile.local
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-for-local-development}
      - ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
      - DATABASE_TYPE=sqlite
      - AWS_ACCESS_KEY_ID=local-development
      - AWS_SECRET_ACCESS_KEY=local-development
      - AWS_REGION=us-east-1
      - BEDROCK_MODEL_ID=mock-model
    volumes:
      - backend_local_data:/app/local_storage
      - backend_local_db:/app
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
    depends_on:
      backend-local:
        condition: service_healthy
    networks:
      - app-network

volumes:
  backend_local_data:
  backend_local_db:

networks:
  app-network:
    driver: bridge
