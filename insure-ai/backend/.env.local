# Local development environment variables
# No AWS services required

# Backend Configuration
SECRET_KEY=local-development-secret-key-change-in-production
DEBUG=true
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Database Configuration (SQLite for local)
DATABASE_TYPE=sqlite
DATABASE_URL=local_data.db

# Mock AWS Configuration (for development)
AWS_ACCESS_KEY_ID=local-development
AWS_SECRET_ACCESS_KEY=local-development
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=************
BEDROCK_MODEL_ID=mock-model

# Local Storage Configuration
LOCAL_STORAGE_PATH=local_storage

# File Upload Settings
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=.pdf,.png,.jpg,.jpeg

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
