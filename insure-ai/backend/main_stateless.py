from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import uvicorn
import os
import logging

from app.core.config import settings
from app.core.security import verify_token
from app.api.routes import auth, documents, forms, templates, users

# Import stateless components
from app.core.database_stateless import init_db
from app.core.storage import init_storage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="FormAir - Insurance Document AI (Stateless)",
    description="AI-powered insurance document processing and form filling - Stateless version for Fargate",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize stateless components
@app.on_event("startup")
async def startup_event():
    """Initialize stateless services on startup"""
    logger.info("Initializing stateless services...")
    
    # Initialize DynamoDB tables
    init_db()
    
    # Initialize S3 storage
    init_storage()
    
    logger.info("Stateless services initialized successfully")

# Security
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from JWT token"""
    token = credentials.credentials
    payload = verify_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return payload

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(users.router, prefix="/api/users", tags=["users"])
app.include_router(documents.router, prefix="/api/documents", tags=["documents"])
app.include_router(forms.router, prefix="/api/forms", tags=["forms"])
app.include_router(templates.router, prefix="/api/templates", tags=["templates"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "FormAir - Insurance Document AI API (Stateless)",
        "version": "1.0.0",
        "docs": "/docs",
        "architecture": "stateless-fargate"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for load balancer"""
    return {
        "status": "healthy", 
        "service": "FormAir API",
        "architecture": "stateless",
        "database": "dynamodb",
        "storage": "s3"
    }

@app.get("/ready")
async def readiness_check():
    """Readiness check for Kubernetes/ECS"""
    # Check if services are ready
    try:
        from app.core.database_stateless import db
        from app.core.storage import storage
        
        # Basic connectivity checks
        db_ready = db.dynamodb is not None
        storage_ready = storage.s3_client is not None
        
        if db_ready and storage_ready:
            return {"status": "ready", "database": "connected", "storage": "connected"}
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "not_ready",
                    "database": "connected" if db_ready else "disconnected",
                    "storage": "connected" if storage_ready else "disconnected"
                }
            )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail={"status": "not_ready", "error": str(e)})

if __name__ == "__main__":
    uvicorn.run(
        "main_stateless:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # Disable reload for production
        log_level="info"
    )
