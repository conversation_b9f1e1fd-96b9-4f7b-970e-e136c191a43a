"""
AWS Bedrock service for production document processing
"""

import json
import boto3
import logging
from typing import Dict, Any, List, Optional
from botocore.exceptions import ClientError, BotoCoreError
import os

from ..core.config import settings

logger = logging.getLogger(__name__)

class BedrockService:
    """AWS Bedrock service for document processing"""
    
    def __init__(self):
        self.region = settings.AWS_REGION
        self.model_id = settings.BEDROCK_MODEL_ID
        
        # Initialize Bedrock client
        try:
            self.bedrock_client = boto3.client(
                'bedrock-runtime',
                region_name=self.region,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID if settings.AWS_ACCESS_KEY_ID != "mock-access-key" else None,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY if settings.AWS_SECRET_ACCESS_KEY != "mock-secret-key" else None
            )
            logger.info(f"Initialized Bedrock client for region {self.region}")
        except Exception as e:
            logger.warning(f"Failed to initialize Bedrock client: {e}. Falling back to mock service.")
            self.bedrock_client = None
    
    async def extract_text_from_document(self, file_path: str) -> Dict[str, Any]:
        """Extract text from document using AWS Bedrock"""
        
        if not self.bedrock_client:
            logger.warning("Bedrock client not available, using mock response")
            return await self._mock_text_extraction(file_path)
        
        try:
            # Read the document file
            with open(file_path, 'rb') as file:
                document_bytes = file.read()
            
            # Prepare the prompt for text extraction
            prompt = """
            Please extract all text content from this document. 
            Focus on extracting:
            - Personal information (names, addresses, phone numbers)
            - Policy details (policy numbers, coverage amounts, dates)
            - Vehicle or property information
            - Any form fields or structured data
            
            Return the extracted text in a clear, structured format.
            """
            
            # Prepare the request body for Claude
            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 4000,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "application/pdf",
                                    "data": document_bytes.hex()
                                }
                            }
                        ]
                    }
                ]
            }
            
            # Call Bedrock
            response = self.bedrock_client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body)
            )
            
            # Parse response
            response_body = json.loads(response['body'].read())
            extracted_text = response_body['content'][0]['text']
            
            return {
                "success": True,
                "extracted_text": extracted_text,
                "model_id": self.model_id,
                "processing_method": "aws_bedrock"
            }
            
        except ClientError as e:
            logger.error(f"AWS Bedrock client error: {e}")
            return await self._mock_text_extraction(file_path)
        except Exception as e:
            logger.error(f"Error extracting text with Bedrock: {e}")
            return await self._mock_text_extraction(file_path)
    
    async def extract_structured_data(
        self, 
        document_text: str, 
        template_fields: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Extract structured data from document text using template fields"""
        
        if not self.bedrock_client:
            logger.warning("Bedrock client not available, using mock response")
            return await self._mock_structured_extraction(document_text, template_fields)
        
        try:
            # Prepare field descriptions for the prompt
            field_descriptions = []
            for field in template_fields:
                field_descriptions.append(f"- {field['name']}: {field.get('description', 'Extract this field')}")
            
            fields_text = "\n".join(field_descriptions)
            
            prompt = f"""
            Please extract the following specific information from this insurance document text:

            {fields_text}

            Document text:
            {document_text}

            Please return the extracted information as a JSON object with the field names as keys and the extracted values as values. 
            If a field cannot be found, use null as the value.
            Also include a confidence score (0-1) for each field.

            Format:
            {{
                "field_name": {{"value": "extracted_value", "confidence": 0.95}},
                "another_field": {{"value": "another_value", "confidence": 0.80}}
            }}
            """
            
            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 2000,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            }
            
            response = self.bedrock_client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body)
            )
            
            response_body = json.loads(response['body'].read())
            extracted_data_text = response_body['content'][0]['text']
            
            # Parse the JSON response
            try:
                extracted_data = json.loads(extracted_data_text)
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract JSON from the text
                import re
                json_match = re.search(r'\{.*\}', extracted_data_text, re.DOTALL)
                if json_match:
                    extracted_data = json.loads(json_match.group())
                else:
                    raise ValueError("Could not parse JSON from response")
            
            return {
                "success": True,
                "extracted_data": extracted_data,
                "model_id": self.model_id,
                "processing_method": "aws_bedrock"
            }
            
        except Exception as e:
            logger.error(f"Error extracting structured data with Bedrock: {e}")
            return await self._mock_structured_extraction(document_text, template_fields)
    
    async def detect_form_fields(self, image_data: bytes) -> Dict[str, Any]:
        """Detect form fields in an image using AWS Bedrock"""
        
        if not self.bedrock_client:
            logger.warning("Bedrock client not available, using mock response")
            return await self._mock_field_detection()
        
        try:
            prompt = """
            Please analyze this form image and identify all the form fields, checkboxes, and input areas.
            For each field, provide:
            - Field type (text, checkbox, dropdown, etc.)
            - Approximate position (describe location)
            - Label or associated text
            - Whether it appears to be required

            Return the information as a JSON array of field objects.
            """
            
            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 3000,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/png",
                                    "data": image_data.hex()
                                }
                            }
                        ]
                    }
                ]
            }
            
            response = self.bedrock_client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body)
            )
            
            response_body = json.loads(response['body'].read())
            detected_fields_text = response_body['content'][0]['text']
            
            # Parse the JSON response
            try:
                detected_fields = json.loads(detected_fields_text)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\[.*\]', detected_fields_text, re.DOTALL)
                if json_match:
                    detected_fields = json.loads(json_match.group())
                else:
                    detected_fields = []
            
            return {
                "success": True,
                "detected_fields": detected_fields,
                "model_id": self.model_id,
                "processing_method": "aws_bedrock"
            }
            
        except Exception as e:
            logger.error(f"Error detecting form fields with Bedrock: {e}")
            return await self._mock_field_detection()
    
    async def _mock_text_extraction(self, file_path: str) -> Dict[str, Any]:
        """Fallback mock text extraction"""
        from .aws_mock import bedrock_service as mock_service
        return await mock_service.extract_text_from_document(file_path)
    
    async def _mock_structured_extraction(self, document_text: str, template_fields: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Fallback mock structured extraction"""
        from .aws_mock import bedrock_service as mock_service
        return await mock_service.extract_structured_data(document_text, template_fields)
    
    async def _mock_field_detection(self) -> Dict[str, Any]:
        """Fallback mock field detection"""
        from .aws_mock import bedrock_service as mock_service
        return await mock_service.detect_form_fields(b"mock_image_data")

# Global instance
bedrock_service = BedrockService()
