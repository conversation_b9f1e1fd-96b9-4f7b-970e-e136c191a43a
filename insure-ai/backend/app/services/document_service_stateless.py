"""
Stateless document service using S3 and DynamoDB
"""

import uuid
from typing import Optional, List, Dict, Any
from fastapi import UploadFile, HTTPException
from datetime import datetime
import logging

from ..core.config import settings
from ..core.database_stateless import get_db
from ..core.storage import storage
from ..models.document import Document, DocumentCreate, DocumentUpdate, DocumentStatus
from ..models.document import ExtractedData, ExtractedDataCreate
from .aws_mock import bedrock_service

logger = logging.getLogger(__name__)

class StatelessDocumentService:
    """Stateless service for document operations using S3 and DynamoDB"""
    
    def __init__(self):
        pass  # No local state initialization needed
    
    async def upload_document(self, file: UploadFile, user_id: str) -> Document:
        """Upload and save a document to S3"""
        # Validate file type
        if not self._is_allowed_file_type(file.filename):
            raise HTTPException(
                status_code=400,
                detail=f"File type not allowed. Allowed types: {settings.ALLOWED_FILE_TYPES}"
            )
        
        # Validate file size
        if file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )
        
        try:
            # Upload to S3
            s3_result = await storage.upload_file(file, folder="documents")
            
            # Create document record
            document_data = DocumentCreate(
                filename=s3_result['filename'],
                original_filename=s3_result['original_filename'],
                file_size=s3_result['size'],
                content_type=s3_result['content_type'],
                user_id=user_id
            )
            
            # Save to DynamoDB
            db = await get_db()
            document_dict = document_data.dict()
            document_dict.update({
                "s3_bucket": s3_result['bucket'],
                "s3_key": s3_result['key'],
                "status": DocumentStatus.UPLOADED
            })
            
            saved_document = await db.insert("documents", document_dict)
            return Document(**saved_document)
            
        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to upload document: {str(e)}")
    
    async def get_document(self, document_id: str, user_id: str) -> Optional[Document]:
        """Get a document by ID"""
        db = await get_db()
        document_data = await db.find_by_id("documents", document_id)
        
        if not document_data:
            return None
        
        # Check if user owns the document
        if document_data.get("user_id") != user_id:
            return None
        
        return Document(**document_data)
    
    async def get_user_documents(self, user_id: str) -> List[Document]:
        """Get all documents for a user"""
        db = await get_db()
        documents_data = await db.find_all_by_field("documents", "user_id", user_id)
        return [Document(**doc) for doc in documents_data]
    
    async def get_document_download_url(self, document_id: str, user_id: str, expiration: int = 3600) -> Optional[str]:
        """Get presigned URL for document download"""
        document = await self.get_document(document_id, user_id)
        if not document:
            return None
        
        # Get S3 key from document record
        s3_key = getattr(document, 's3_key', None)
        if not s3_key:
            logger.error(f"Document {document_id} missing S3 key")
            return None
        
        return await storage.get_presigned_url(s3_key, expiration)
    
    async def delete_document(self, document_id: str, user_id: str) -> bool:
        """Delete a document"""
        document = await self.get_document(document_id, user_id)
        if not document:
            return False
        
        try:
            # Delete from S3
            s3_key = getattr(document, 's3_key', None)
            if s3_key:
                await storage.delete_file(s3_key)
            
            # Delete from DynamoDB
            db = await get_db()
            return await db.delete("documents", document_id)
            
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            return False
    
    async def update_document_status(self, document_id: str, status: DocumentStatus, error_message: str = None) -> Optional[Document]:
        """Update document processing status"""
        db = await get_db()
        
        updates = {"status": status}
        if error_message:
            updates["processing_error"] = error_message
        
        updated_document = await db.update("documents", document_id, updates)
        
        if updated_document:
            return Document(**updated_document)
        return None
    
    async def process_document(self, document_id: str, user_id: str) -> Document:
        """Process document for text extraction"""
        document = await self.get_document(document_id, user_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        if document.status != DocumentStatus.UPLOADED:
            raise HTTPException(status_code=400, detail="Document already processed or processing")
        
        try:
            # Update status to processing
            await self.update_document_status(document_id, DocumentStatus.PROCESSING)
            
            # Download file from S3 for processing
            s3_key = getattr(document, 's3_key', None)
            if not s3_key:
                raise HTTPException(status_code=500, detail="Document S3 key not found")
            
            file_data = await storage.download_file(s3_key)
            if not file_data:
                raise HTTPException(status_code=500, detail="Failed to download document from S3")
            
            # Save temporarily for processing (in memory or temp file)
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{document.filename.split('.')[-1]}") as temp_file:
                temp_file.write(file_data)
                temp_file_path = temp_file.name
            
            try:
                # Extract text using Bedrock
                result = await bedrock_service.extract_text_from_document(temp_file_path)
                
                if result.get("success"):
                    # Update document with extracted text
                    updates = {
                        "status": DocumentStatus.COMPLETED,
                        "extracted_text": result.get("extracted_text")
                    }
                    updated_document = await self.update_document_status(document_id, DocumentStatus.COMPLETED)
                    
                    # Update with extracted text
                    db = await get_db()
                    await db.update("documents", document_id, {"extracted_text": result.get("extracted_text")})
                    
                    return updated_document
                else:
                    await self.update_document_status(document_id, DocumentStatus.FAILED, "Text extraction failed")
                    raise HTTPException(status_code=500, detail="Text extraction failed")
                    
            finally:
                # Clean up temp file
                import os
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                    
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing document {document_id}: {e}")
            await self.update_document_status(document_id, DocumentStatus.FAILED, str(e))
            raise HTTPException(status_code=500, detail=f"Document processing failed: {str(e)}")
    
    async def extract_structured_data(
        self, 
        document_id: str, 
        template_id: str, 
        user_id: str
    ) -> ExtractedData:
        """Extract structured data from document using template"""
        document = await self.get_document(document_id, user_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        if not document.extracted_text:
            raise HTTPException(status_code=400, detail="Document text not extracted yet")
        
        # Get template
        db = await get_db()
        template_data = await db.find_by_id("templates", template_id)
        if not template_data:
            raise HTTPException(status_code=404, detail="Template not found")
        
        try:
            # Extract structured data using Bedrock
            result = await bedrock_service.extract_structured_data(
                document.extracted_text,
                template_data.get("fields", [])
            )
            
            if result.get("success"):
                # Save extracted data
                extracted_data = ExtractedDataCreate(
                    document_id=document_id,
                    template_id=template_id,
                    user_id=user_id,
                    extracted_values=result.get("extracted_data", {})
                )
                
                saved_data = await db.insert("extracted_data", extracted_data.dict())
                return ExtractedData(**saved_data)
            else:
                raise HTTPException(status_code=500, detail="Structured data extraction failed")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error extracting structured data: {e}")
            raise HTTPException(status_code=500, detail=f"Structured data extraction failed: {str(e)}")
    
    async def get_extracted_data(self, document_id: str, user_id: str) -> List[ExtractedData]:
        """Get all extracted data for a document"""
        db = await get_db()
        extracted_data_list = await db.find_all_by_field("extracted_data", "document_id", document_id)
        
        # Filter by user
        user_data = [data for data in extracted_data_list if data.get("user_id") == user_id]
        
        return [ExtractedData(**data) for data in user_data]
    
    def _is_allowed_file_type(self, filename: str) -> bool:
        """Check if file type is allowed"""
        if not filename:
            return False
        
        import os
        file_extension = os.path.splitext(filename)[1].lower()
        return file_extension in settings.ALLOWED_FILE_TYPES

# Global service instance
document_service = StatelessDocumentService()
