"""
Local document service using local storage and SQLite
"""

import uuid
from typing import Optional, List, Dict, Any
from fastapi import UploadFile, HTTPException
from datetime import datetime
import logging

from ..core.config import settings
from ..core.database_local import get_db
from ..core.storage_local import storage
from ..models.document import Document, DocumentCreate, DocumentUpdate, DocumentStatus
from ..models.document import ExtractedData, ExtractedDataCreate
from .aws_mock import bedrock_service

logger = logging.getLogger(__name__)

class LocalDocumentService:
    """Local service for document operations using local storage and SQLite"""
    
    def __init__(self):
        pass  # No initialization needed for local service
    
    async def upload_document(self, file: UploadFile, user_id: str) -> Document:
        """Upload and save a document to local storage"""
        # Validate file type
        if not self._is_allowed_file_type(file.filename):
            raise HTTPException(
                status_code=400,
                detail=f"File type not allowed. Allowed types: {settings.ALLOWED_FILE_TYPES}"
            )
        
        # Validate file size
        if file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )
        
        try:
            # Upload to local storage
            storage_result = await storage.upload_file(file, folder="documents")
            
            # Create document record
            document_data = DocumentCreate(
                filename=storage_result['filename'],
                original_filename=storage_result['original_filename'],
                file_size=storage_result['size'],
                content_type=storage_result['content_type'],
                user_id=user_id
            )
            
            # Save to local database
            db = await get_db()
            document_dict = document_data.dict()
            document_dict.update({
                "local_bucket": storage_result['bucket'],
                "local_key": storage_result['key'],
                "status": DocumentStatus.UPLOADED
            })
            
            saved_document = await db.insert("documents", document_dict)
            return Document(**saved_document)
            
        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to upload document: {str(e)}")
    
    async def get_document(self, document_id: str, user_id: str) -> Optional[Document]:
        """Get a document by ID"""
        db = await get_db()
        document_data = await db.find_by_id("documents", document_id)
        
        if not document_data:
            return None
        
        # Check if user owns the document
        if document_data.get("user_id") != user_id:
            return None
        
        return Document(**document_data)
    
    async def get_user_documents(self, user_id: str) -> List[Document]:
        """Get all documents for a user"""
        db = await get_db()
        documents_data = await db.find_all_by_field("documents", "user_id", user_id)
        return [Document(**doc) for doc in documents_data]
    
    async def get_document_download_url(self, document_id: str, user_id: str, expiration: int = 3600) -> Optional[str]:
        """Get local URL for document download"""
        document = await self.get_document(document_id, user_id)
        if not document:
            return None
        
        # Get local key from document record
        local_key = getattr(document, 'local_key', None)
        if not local_key:
            logger.error(f"Document {document_id} missing local key")
            return None
        
        return await storage.get_presigned_url(local_key, expiration)
    
    async def get_document_content(self, document_id: str, user_id: str) -> Optional[bytes]:
        """Get document content directly (for local development)"""
        document = await self.get_document(document_id, user_id)
        if not document:
            return None
        
        local_key = getattr(document, 'local_key', None)
        if not local_key:
            return None
        
        return await storage.download_file(local_key)
    
    async def delete_document(self, document_id: str, user_id: str) -> bool:
        """Delete a document"""
        document = await self.get_document(document_id, user_id)
        if not document:
            return False
        
        try:
            # Delete from local storage
            local_key = getattr(document, 'local_key', None)
            if local_key:
                await storage.delete_file(local_key)
            
            # Delete from database
            db = await get_db()
            return await db.delete("documents", document_id)
            
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            return False
    
    async def update_document_status(self, document_id: str, status: DocumentStatus, error_message: str = None) -> Optional[Document]:
        """Update document processing status"""
        db = await get_db()
        
        updates = {"status": status}
        if error_message:
            updates["processing_error"] = error_message
        
        updated_document = await db.update("documents", document_id, updates)
        
        if updated_document:
            return Document(**updated_document)
        return None
    
    async def process_document(self, document_id: str, user_id: str) -> Document:
        """Process document for text extraction"""
        document = await self.get_document(document_id, user_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        if document.status != DocumentStatus.UPLOADED:
            raise HTTPException(status_code=400, detail="Document already processed or processing")
        
        try:
            # Update status to processing
            await self.update_document_status(document_id, DocumentStatus.PROCESSING)
            
            # Get file content from local storage
            local_key = getattr(document, 'local_key', None)
            if not local_key:
                raise HTTPException(status_code=500, detail="Document local key not found")
            
            file_data = await storage.download_file(local_key)
            if not file_data:
                raise HTTPException(status_code=500, detail="Failed to download document from local storage")
            
            # Save temporarily for processing
            import tempfile
            import os
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{document.filename.split('.')[-1]}") as temp_file:
                temp_file.write(file_data)
                temp_file_path = temp_file.name
            
            try:
                # Extract text using mock Bedrock service
                result = await bedrock_service.extract_text_from_document(temp_file_path)
                
                if result.get("success"):
                    # Update document with extracted text
                    db = await get_db()
                    updates = {
                        "status": DocumentStatus.COMPLETED,
                        "extracted_text": result.get("extracted_text")
                    }
                    updated_document = await db.update("documents", document_id, updates)
                    
                    if updated_document:
                        return Document(**updated_document)
                    else:
                        raise HTTPException(status_code=500, detail="Failed to update document")
                else:
                    await self.update_document_status(document_id, DocumentStatus.FAILED, "Text extraction failed")
                    raise HTTPException(status_code=500, detail="Text extraction failed")
                    
            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                    
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing document {document_id}: {e}")
            await self.update_document_status(document_id, DocumentStatus.FAILED, str(e))
            raise HTTPException(status_code=500, detail=f"Document processing failed: {str(e)}")
    
    async def extract_structured_data(
        self, 
        document_id: str, 
        template_id: str, 
        user_id: str
    ) -> ExtractedData:
        """Extract structured data from document using template"""
        document = await self.get_document(document_id, user_id)
        if not document:
            raise HTTPException(status_code=404, detail="Document not found")
        
        if not document.extracted_text:
            raise HTTPException(status_code=400, detail="Document text not extracted yet")
        
        # Get template
        db = await get_db()
        template_data = await db.find_by_id("templates", template_id)
        if not template_data:
            raise HTTPException(status_code=404, detail="Template not found")
        
        try:
            # Extract structured data using mock Bedrock service
            result = await bedrock_service.extract_structured_data(
                document.extracted_text,
                template_data.get("fields", [])
            )
            
            if result.get("success"):
                # Save extracted data
                extracted_data = ExtractedDataCreate(
                    document_id=document_id,
                    template_id=template_id,
                    user_id=user_id,
                    extracted_values=result.get("extracted_data", {})
                )
                
                saved_data = await db.insert("extracted_data", extracted_data.dict())
                return ExtractedData(**saved_data)
            else:
                raise HTTPException(status_code=500, detail="Structured data extraction failed")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error extracting structured data: {e}")
            raise HTTPException(status_code=500, detail=f"Structured data extraction failed: {str(e)}")
    
    async def get_extracted_data(self, document_id: str, user_id: str) -> List[ExtractedData]:
        """Get all extracted data for a document"""
        db = await get_db()
        extracted_data_list = await db.find_all_by_field("extracted_data", "document_id", document_id)
        
        # Filter by user
        user_data = [data for data in extracted_data_list if data.get("user_id") == user_id]
        
        return [ExtractedData(**data) for data in user_data]
    
    def _is_allowed_file_type(self, filename: str) -> bool:
        """Check if file type is allowed"""
        if not filename:
            return False
        
        import os
        file_extension = os.path.splitext(filename)[1].lower()
        return file_extension in settings.ALLOWED_FILE_TYPES

# Global service instance
document_service = LocalDocumentService()
