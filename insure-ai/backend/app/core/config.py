from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    """Application settings"""

    # App settings
    APP_NAME: str = "FormAir"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True

    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALGORITHM: str = "HS256"

    # CORS
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001"
    ]

    # Production settings
    @property
    def allowed_origins_list(self) -> List[str]:
        """Get allowed origins as a list, supporting environment variable override"""
        env_origins = os.getenv("ALLOWED_ORIGINS")
        if env_origins:
            return [origin.strip() for origin in env_origins.split(",")]
        return self.ALLOWED_ORIGINS

    # File upload settings
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [".pdf", ".png", ".jpg", ".jpeg"]
    UPLOAD_DIR: str = "uploads"
    FORMS_DIR: str = "forms"
    TEMPLATES_DIR: str = "templates"
    EXPORTS_DIR: str = "exports"

    # Database settings (using JSON files for simplicity)
    DATABASE_TYPE: str = "json"  # json, sqlite, postgresql
    DATABASE_URL: str = "data/"

    # AWS settings (mock for development)
    AWS_REGION: str = "us-east-1"
    AWS_ACCESS_KEY_ID: str = "mock-access-key"
    AWS_SECRET_ACCESS_KEY: str = "mock-secret-key"
    BEDROCK_MODEL_ID: str = "anthropic.claude-3-sonnet-20240229-v1:0"

    # Default users for development
    DEFAULT_USERS: List[dict] = [
        {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "admin123",
            "is_admin": True
        },
        {
            "username": "user1",
            "email": "<EMAIL>",
            "password": "user123",
            "is_admin": False
        },
        {
            "username": "demo",
            "email": "<EMAIL>",
            "password": "demo123",
            "is_admin": False
        }
    ]

    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Ensure directories exist
for directory in [
    settings.UPLOAD_DIR,
    settings.FORMS_DIR,
    settings.TEMPLATES_DIR,
    settings.EXPORTS_DIR,
    settings.DATABASE_URL
]:
    os.makedirs(directory, exist_ok=True)
