"""
Stateless file storage using AWS S3
"""

import boto3
import aioboto3
import uuid
import os
import logging
from typing import Optional, Dict, Any, BinaryIO
from botocore.exceptions import ClientError
from fastapi import UploadFile
import aiofiles

from .config import settings

logger = logging.getLogger(__name__)

class S3Storage:
    """Stateless file storage using AWS S3"""
    
    def __init__(self):
        self.region = settings.AWS_REGION
        self.bucket_name = f"insure-ai-storage-{settings.AWS_ACCOUNT_ID}" if hasattr(settings, 'AWS_ACCOUNT_ID') else "insure-ai-storage"
        
        # Initialize clients
        self._init_clients()
    
    def _init_clients(self):
        """Initialize S3 clients"""
        try:
            # Sync client for initialization
            self.s3_client = boto3.client(
                's3',
                region_name=self.region,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID if settings.AWS_ACCESS_KEY_ID != "mock-access-key" else None,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY if settings.AWS_SECRET_ACCESS_KEY != "mock-secret-key" else None
            )
            
            # Async session for runtime operations
            self.session = aioboto3.Session()
            
            logger.info(f"Initialized S3 clients for region {self.region}")
        except Exception as e:
            logger.error(f"Failed to initialize S3 clients: {e}")
            self.s3_client = None
            self.session = None
    
    async def _get_async_client(self):
        """Get async S3 client"""
        if not self.session:
            raise Exception("S3 session not initialized")
        
        return self.session.client(
            's3',
            region_name=self.region,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID if settings.AWS_ACCESS_KEY_ID != "mock-access-key" else None,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY if settings.AWS_SECRET_ACCESS_KEY != "mock-secret-key" else None
        )
    
    def create_bucket(self):
        """Create S3 bucket if it doesn't exist"""
        if not self.s3_client:
            logger.warning("S3 client not available, skipping bucket creation")
            return
        
        try:
            # Check if bucket exists
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"Bucket {self.bucket_name} already exists")
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                # Bucket doesn't exist, create it
                try:
                    if self.region == 'us-east-1':
                        self.s3_client.create_bucket(Bucket=self.bucket_name)
                    else:
                        self.s3_client.create_bucket(
                            Bucket=self.bucket_name,
                            CreateBucketConfiguration={'LocationConstraint': self.region}
                        )
                    
                    # Enable versioning
                    self.s3_client.put_bucket_versioning(
                        Bucket=self.bucket_name,
                        VersioningConfiguration={'Status': 'Enabled'}
                    )
                    
                    # Set bucket policy for private access
                    self._set_bucket_policy()
                    
                    logger.info(f"Created bucket {self.bucket_name}")
                except Exception as create_error:
                    logger.error(f"Failed to create bucket {self.bucket_name}: {create_error}")
            else:
                logger.error(f"Error checking bucket {self.bucket_name}: {e}")
    
    def _set_bucket_policy(self):
        """Set bucket policy for secure access"""
        bucket_policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "DenyPublicAccess",
                    "Effect": "Deny",
                    "Principal": "*",
                    "Action": "s3:*",
                    "Resource": [
                        f"arn:aws:s3:::{self.bucket_name}",
                        f"arn:aws:s3:::{self.bucket_name}/*"
                    ],
                    "Condition": {
                        "Bool": {
                            "aws:SecureTransport": "false"
                        }
                    }
                }
            ]
        }
        
        try:
            import json
            self.s3_client.put_bucket_policy(
                Bucket=self.bucket_name,
                Policy=json.dumps(bucket_policy)
            )
        except Exception as e:
            logger.warning(f"Failed to set bucket policy: {e}")
    
    async def upload_file(self, file: UploadFile, folder: str = "uploads") -> Dict[str, Any]:
        """Upload file to S3"""
        try:
            s3_client = await self._get_async_client()
            
            # Generate unique key
            file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            s3_key = f"{folder}/{unique_filename}"
            
            # Upload file
            await s3_client.upload_fileobj(
                file.file,
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    'ContentType': file.content_type or 'application/octet-stream',
                    'Metadata': {
                        'original_filename': file.filename or 'unknown',
                        'upload_timestamp': str(int(os.time()))
                    }
                }
            )
            
            return {
                'bucket': self.bucket_name,
                'key': s3_key,
                'filename': unique_filename,
                'original_filename': file.filename,
                'content_type': file.content_type,
                'size': file.size
            }
            
        except Exception as e:
            logger.error(f"Error uploading file to S3: {e}")
            raise
    
    async def upload_bytes(self, data: bytes, filename: str, folder: str = "uploads", content_type: str = "application/octet-stream") -> Dict[str, Any]:
        """Upload bytes data to S3"""
        try:
            s3_client = await self._get_async_client()
            
            # Generate unique key
            file_extension = os.path.splitext(filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            s3_key = f"{folder}/{unique_filename}"
            
            # Upload data
            await s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=data,
                ContentType=content_type,
                Metadata={
                    'original_filename': filename,
                    'upload_timestamp': str(int(os.time()))
                }
            )
            
            return {
                'bucket': self.bucket_name,
                'key': s3_key,
                'filename': unique_filename,
                'original_filename': filename,
                'content_type': content_type,
                'size': len(data)
            }
            
        except Exception as e:
            logger.error(f"Error uploading bytes to S3: {e}")
            raise
    
    async def download_file(self, s3_key: str) -> Optional[bytes]:
        """Download file from S3"""
        try:
            s3_client = await self._get_async_client()
            
            response = await s3_client.get_object(Bucket=self.bucket_name, Key=s3_key)
            return await response['Body'].read()
            
        except Exception as e:
            logger.error(f"Error downloading file {s3_key} from S3: {e}")
            return None
    
    async def delete_file(self, s3_key: str) -> bool:
        """Delete file from S3"""
        try:
            s3_client = await self._get_async_client()
            
            await s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
            return True
            
        except Exception as e:
            logger.error(f"Error deleting file {s3_key} from S3: {e}")
            return False
    
    async def get_presigned_url(self, s3_key: str, expiration: int = 3600, method: str = 'get_object') -> Optional[str]:
        """Generate presigned URL for file access"""
        try:
            s3_client = await self._get_async_client()
            
            url = await s3_client.generate_presigned_url(
                method,
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            return url
            
        except Exception as e:
            logger.error(f"Error generating presigned URL for {s3_key}: {e}")
            return None
    
    async def get_presigned_upload_url(self, s3_key: str, content_type: str = "application/octet-stream", expiration: int = 3600) -> Optional[Dict[str, Any]]:
        """Generate presigned URL for file upload"""
        try:
            s3_client = await self._get_async_client()
            
            response = await s3_client.generate_presigned_post(
                Bucket=self.bucket_name,
                Key=s3_key,
                Fields={'Content-Type': content_type},
                Conditions=[
                    {'Content-Type': content_type},
                    ['content-length-range', 1, settings.MAX_FILE_SIZE]
                ],
                ExpiresIn=expiration
            )
            return response
            
        except Exception as e:
            logger.error(f"Error generating presigned upload URL for {s3_key}: {e}")
            return None
    
    async def file_exists(self, s3_key: str) -> bool:
        """Check if file exists in S3"""
        try:
            s3_client = await self._get_async_client()
            
            await s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            logger.error(f"Error checking file existence {s3_key}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error checking file existence {s3_key}: {e}")
            return False
    
    async def list_files(self, prefix: str = "", max_keys: int = 1000) -> List[Dict[str, Any]]:
        """List files in S3 bucket"""
        try:
            s3_client = await self._get_async_client()
            
            response = await s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix,
                MaxKeys=max_keys
            )
            
            files = []
            if 'Contents' in response:
                for obj in response['Contents']:
                    files.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'],
                        'etag': obj['ETag']
                    })
            
            return files
            
        except Exception as e:
            logger.error(f"Error listing files with prefix {prefix}: {e}")
            return []

# Global storage instance
storage = S3Storage()

def init_storage():
    """Initialize storage"""
    if storage.s3_client:
        storage.create_bucket()
        logger.info("S3 storage initialized")
    else:
        logger.warning("S3 not available, skipping initialization")
