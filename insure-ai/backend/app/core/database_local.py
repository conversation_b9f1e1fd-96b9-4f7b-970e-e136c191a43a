"""
Local database implementation using SQLite for development
Mimics the DynamoDB interface but runs locally
"""

import sqlite3
import json
import uuid
import os
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging
import aiosqlite

from .config import settings

logger = logging.getLogger(__name__)

class LocalDatabase:
    """Local database using SQLite that mimics DynamoDB interface"""
    
    def __init__(self, db_path: str = "local_data.db"):
        self.db_path = db_path
        self.tables = {
            "users": "users",
            "documents": "documents", 
            "forms": "forms",
            "templates": "templates",
            "template_fields": "template_fields",
            "extracted_data": "extracted_data",
            "form_fields": "form_fields"
        }
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database and tables"""
        try:
            # Create database file if it doesn't exist
            conn = sqlite3.connect(self.db_path)
            
            # Create tables
            for table_name in self.tables.values():
                conn.execute(f'''
                    CREATE TABLE IF NOT EXISTS {table_name} (
                        id TEXT PRIMARY KEY,
                        data TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL
                    )
                ''')
            
            # Create indexes for common queries
            for table_name in self.tables.values():
                conn.execute(f'''
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_created_at 
                    ON {table_name}(created_at)
                ''')
            
            conn.commit()
            conn.close()
            
            logger.info(f"Initialized local SQLite database at {self.db_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize local database: {e}")
            raise
    
    async def insert(self, table: str, record: Dict[str, Any]) -> Dict[str, Any]:
        """Insert a record"""
        try:
            # Add ID if not present
            if 'id' not in record:
                record['id'] = str(uuid.uuid4())
            
            # Add timestamps
            now = datetime.utcnow().isoformat()
            if 'created_at' not in record:
                record['created_at'] = now
            record['updated_at'] = now
            
            # Convert record to JSON for storage
            data_json = json.dumps(record)
            
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    f'INSERT INTO {self.tables[table]} (id, data, created_at, updated_at) VALUES (?, ?, ?, ?)',
                    (record['id'], data_json, record['created_at'], record['updated_at'])
                )
                await db.commit()
            
            return record
            
        except Exception as e:
            logger.error(f"Error inserting record into {table}: {e}")
            raise
    
    async def find_by_id(self, table: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Find record by ID"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(
                    f'SELECT data FROM {self.tables[table]} WHERE id = ?',
                    (record_id,)
                ) as cursor:
                    row = await cursor.fetchone()
                    
                    if row:
                        return json.loads(row[0])
                    return None
                    
        except Exception as e:
            logger.error(f"Error finding record {record_id} in {table}: {e}")
            return None
    
    async def find_by_field(self, table: str, field: str, value: Any) -> Optional[Dict[str, Any]]:
        """Find first record by field value"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(
                    f'SELECT data FROM {self.tables[table]}',
                ) as cursor:
                    async for row in cursor:
                        record = json.loads(row[0])
                        if record.get(field) == value:
                            return record
                    return None
                    
        except Exception as e:
            logger.error(f"Error finding record by {field}={value} in {table}: {e}")
            return None
    
    async def find_all_by_field(self, table: str, field: str, value: Any) -> List[Dict[str, Any]]:
        """Find all records by field value"""
        try:
            results = []
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(
                    f'SELECT data FROM {self.tables[table]}',
                ) as cursor:
                    async for row in cursor:
                        record = json.loads(row[0])
                        if record.get(field) == value:
                            results.append(record)
            
            return results
            
        except Exception as e:
            logger.error(f"Error finding records by {field}={value} in {table}: {e}")
            return []
    
    async def update(self, table: str, record_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a record"""
        try:
            # First get the existing record
            existing = await self.find_by_id(table, record_id)
            if not existing:
                return None
            
            # Apply updates
            existing.update(updates)
            existing['updated_at'] = datetime.utcnow().isoformat()
            
            # Save back to database
            data_json = json.dumps(existing)
            
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    f'UPDATE {self.tables[table]} SET data = ?, updated_at = ? WHERE id = ?',
                    (data_json, existing['updated_at'], record_id)
                )
                await db.commit()
            
            return existing
            
        except Exception as e:
            logger.error(f"Error updating record {record_id} in {table}: {e}")
            return None
    
    async def delete(self, table: str, record_id: str) -> bool:
        """Delete a record"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute(
                    f'DELETE FROM {self.tables[table]} WHERE id = ?',
                    (record_id,)
                )
                await db.commit()
                
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"Error deleting record {record_id} from {table}: {e}")
            return False
    
    async def read_table(self, table: str) -> List[Dict[str, Any]]:
        """Read entire table"""
        try:
            results = []
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(
                    f'SELECT data FROM {self.tables[table]} ORDER BY created_at',
                ) as cursor:
                    async for row in cursor:
                        results.append(json.loads(row[0]))
            
            return results
            
        except Exception as e:
            logger.error(f"Error reading table {table}: {e}")
            return []

# Global database instance
db = LocalDatabase()

async def get_db():
    """Get database instance"""
    return db

def init_db():
    """Initialize database with default data"""
    try:
        # Create default users if none exist
        async def _init_default_data():
            users = await db.read_table("users")
            if not users:
                from ..core.security import hash_password
                for user_data in settings.DEFAULT_USERS:
                    user_record = {
                        "username": user_data["username"],
                        "email": user_data["email"],
                        "hashed_password": hash_password(user_data["password"]),
                        "is_admin": user_data["is_admin"],
                        "is_active": True
                    }
                    await db.insert("users", user_record)
                logger.info("Created default users")
        
        # Run the async initialization
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(_init_default_data())
        loop.close()
        
        logger.info("Local database initialized with default data")
        
    except Exception as e:
        logger.error(f"Error initializing default data: {e}")
