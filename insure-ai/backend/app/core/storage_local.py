"""
Local file storage that mimics S3 interface for development
"""

import os
import uuid
import shutil
import logging
from typing import Optional, Dict, Any, List
from fastapi import UploadFile
import aiofiles
import json
from urllib.parse import quote

from .config import settings

logger = logging.getLogger(__name__)

class LocalStorage:
    """Local file storage that mimics S3 interface"""
    
    def __init__(self, base_path: str = "local_storage"):
        self.base_path = base_path
        self.bucket_name = "local-bucket"
        
        # Create storage directories
        self._init_storage()
    
    def _init_storage(self):
        """Initialize local storage directories"""
        try:
            # Create base directory
            os.makedirs(self.base_path, exist_ok=True)
            
            # Create folder structure
            folders = ["documents", "forms", "templates", "exports"]
            for folder in folders:
                os.makedirs(os.path.join(self.base_path, folder), exist_ok=True)
            
            # Create metadata directory
            os.makedirs(os.path.join(self.base_path, ".metadata"), exist_ok=True)
            
            logger.info(f"Initialized local storage at {self.base_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize local storage: {e}")
            raise
    
    def _get_file_path(self, s3_key: str) -> str:
        """Get local file path from S3 key"""
        return os.path.join(self.base_path, s3_key)
    
    def _get_metadata_path(self, s3_key: str) -> str:
        """Get metadata file path for S3 key"""
        metadata_key = s3_key.replace("/", "_") + ".json"
        return os.path.join(self.base_path, ".metadata", metadata_key)
    
    async def upload_file(self, file: UploadFile, folder: str = "uploads") -> Dict[str, Any]:
        """Upload file to local storage"""
        try:
            # Generate unique key
            file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            s3_key = f"{folder}/{unique_filename}"
            
            # Get file path
            file_path = self._get_file_path(s3_key)
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
            
            # Save metadata
            metadata = {
                'original_filename': file.filename or 'unknown',
                'content_type': file.content_type or 'application/octet-stream',
                'size': len(content),
                'upload_timestamp': str(int(os.time())),
                's3_key': s3_key,
                'bucket': self.bucket_name
            }
            
            metadata_path = self._get_metadata_path(s3_key)
            async with aiofiles.open(metadata_path, 'w') as f:
                await f.write(json.dumps(metadata))
            
            return {
                'bucket': self.bucket_name,
                'key': s3_key,
                'filename': unique_filename,
                'original_filename': file.filename,
                'content_type': file.content_type,
                'size': len(content)
            }
            
        except Exception as e:
            logger.error(f"Error uploading file to local storage: {e}")
            raise
    
    async def upload_bytes(self, data: bytes, filename: str, folder: str = "uploads", content_type: str = "application/octet-stream") -> Dict[str, Any]:
        """Upload bytes data to local storage"""
        try:
            # Generate unique key
            file_extension = os.path.splitext(filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            s3_key = f"{folder}/{unique_filename}"
            
            # Get file path
            file_path = self._get_file_path(s3_key)
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(data)
            
            # Save metadata
            metadata = {
                'original_filename': filename,
                'content_type': content_type,
                'size': len(data),
                'upload_timestamp': str(int(os.time())),
                's3_key': s3_key,
                'bucket': self.bucket_name
            }
            
            metadata_path = self._get_metadata_path(s3_key)
            async with aiofiles.open(metadata_path, 'w') as f:
                await f.write(json.dumps(metadata))
            
            return {
                'bucket': self.bucket_name,
                'key': s3_key,
                'filename': unique_filename,
                'original_filename': filename,
                'content_type': content_type,
                'size': len(data)
            }
            
        except Exception as e:
            logger.error(f"Error uploading bytes to local storage: {e}")
            raise
    
    async def download_file(self, s3_key: str) -> Optional[bytes]:
        """Download file from local storage"""
        try:
            file_path = self._get_file_path(s3_key)
            
            if not os.path.exists(file_path):
                return None
            
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()
                
        except Exception as e:
            logger.error(f"Error downloading file {s3_key} from local storage: {e}")
            return None
    
    async def delete_file(self, s3_key: str) -> bool:
        """Delete file from local storage"""
        try:
            file_path = self._get_file_path(s3_key)
            metadata_path = self._get_metadata_path(s3_key)
            
            # Delete file
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # Delete metadata
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting file {s3_key} from local storage: {e}")
            return False
    
    async def get_presigned_url(self, s3_key: str, expiration: int = 3600, method: str = 'get_object') -> Optional[str]:
        """Generate local URL for file access"""
        try:
            file_path = self._get_file_path(s3_key)
            
            if not os.path.exists(file_path):
                return None
            
            # For local development, return a file:// URL or local server URL
            # In a real local server, you'd serve this through FastAPI
            return f"/local-files/{quote(s3_key)}"
            
        except Exception as e:
            logger.error(f"Error generating local URL for {s3_key}: {e}")
            return None
    
    async def get_presigned_upload_url(self, s3_key: str, content_type: str = "application/octet-stream", expiration: int = 3600) -> Optional[Dict[str, Any]]:
        """Generate local upload URL (for local development, return upload endpoint)"""
        try:
            return {
                "url": "/local-upload",
                "fields": {
                    "key": s3_key,
                    "Content-Type": content_type
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating local upload URL for {s3_key}: {e}")
            return None
    
    async def file_exists(self, s3_key: str) -> bool:
        """Check if file exists in local storage"""
        try:
            file_path = self._get_file_path(s3_key)
            return os.path.exists(file_path)
            
        except Exception as e:
            logger.error(f"Error checking file existence {s3_key}: {e}")
            return False
    
    async def list_files(self, prefix: str = "", max_keys: int = 1000) -> List[Dict[str, Any]]:
        """List files in local storage"""
        try:
            files = []
            search_path = os.path.join(self.base_path, prefix) if prefix else self.base_path
            
            if os.path.exists(search_path):
                for root, dirs, filenames in os.walk(search_path):
                    # Skip metadata directory
                    if '.metadata' in root:
                        continue
                        
                    for filename in filenames:
                        if len(files) >= max_keys:
                            break
                            
                        file_path = os.path.join(root, filename)
                        relative_path = os.path.relpath(file_path, self.base_path)
                        s3_key = relative_path.replace(os.sep, '/')
                        
                        stat = os.stat(file_path)
                        files.append({
                            'key': s3_key,
                            'size': stat.st_size,
                            'last_modified': stat.st_mtime,
                            'etag': f'"{hash(s3_key)}"'
                        })
            
            return files
            
        except Exception as e:
            logger.error(f"Error listing files with prefix {prefix}: {e}")
            return []
    
    def create_bucket(self):
        """Create bucket (already done in __init__ for local storage)"""
        logger.info(f"Local storage bucket '{self.bucket_name}' ready")

# Global storage instance
storage = LocalStorage()

def init_storage():
    """Initialize storage"""
    storage.create_bucket()
    logger.info("Local storage initialized")
