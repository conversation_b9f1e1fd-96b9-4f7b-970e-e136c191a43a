"""
Stateless database implementation using AWS DynamoDB
"""

import boto3
import aioboto3
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from botocore.exceptions import ClientError
import logging

from .config import settings

logger = logging.getLogger(__name__)

class DynamoDBDatabase:
    """Stateless database using AWS DynamoDB"""
    
    def __init__(self):
        self.region = settings.AWS_REGION
        self.table_prefix = "insure-ai"
        
        # Table definitions
        self.tables = {
            "users": f"{self.table_prefix}-users",
            "documents": f"{self.table_prefix}-documents", 
            "forms": f"{self.table_prefix}-forms",
            "templates": f"{self.table_prefix}-templates",
            "template_fields": f"{self.table_prefix}-template-fields",
            "extracted_data": f"{self.table_prefix}-extracted-data",
            "form_fields": f"{self.table_prefix}-form-fields"
        }
        
        # Initialize clients
        self._init_clients()
    
    def _init_clients(self):
        """Initialize DynamoDB clients"""
        try:
            # Sync client for initialization
            self.dynamodb = boto3.resource(
                'dynamodb',
                region_name=self.region,
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID if settings.AWS_ACCESS_KEY_ID != "mock-access-key" else None,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY if settings.AWS_SECRET_ACCESS_KEY != "mock-secret-key" else None
            )
            
            # Async session for runtime operations
            self.session = aioboto3.Session()
            
            logger.info(f"Initialized DynamoDB clients for region {self.region}")
        except Exception as e:
            logger.error(f"Failed to initialize DynamoDB clients: {e}")
            self.dynamodb = None
            self.session = None
    
    async def _get_async_client(self):
        """Get async DynamoDB client"""
        if not self.session:
            raise Exception("DynamoDB session not initialized")
        
        return self.session.resource(
            'dynamodb',
            region_name=self.region,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID if settings.AWS_ACCESS_KEY_ID != "mock-access-key" else None,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY if settings.AWS_SECRET_ACCESS_KEY != "mock-secret-key" else None
        )
    
    def create_tables(self):
        """Create DynamoDB tables if they don't exist"""
        if not self.dynamodb:
            logger.warning("DynamoDB not available, skipping table creation")
            return
        
        for table_name in self.tables.values():
            try:
                table = self.dynamodb.Table(table_name)
                table.load()
                logger.info(f"Table {table_name} already exists")
            except ClientError as e:
                if e.response['Error']['Code'] == 'ResourceNotFoundException':
                    self._create_table(table_name)
                else:
                    logger.error(f"Error checking table {table_name}: {e}")
    
    def _create_table(self, table_name: str):
        """Create a single DynamoDB table"""
        try:
            table = self.dynamodb.create_table(
                TableName=table_name,
                KeySchema=[
                    {
                        'AttributeName': 'id',
                        'KeyType': 'HASH'
                    }
                ],
                AttributeDefinitions=[
                    {
                        'AttributeName': 'id',
                        'AttributeType': 'S'
                    }
                ],
                BillingMode='PAY_PER_REQUEST'
            )
            
            # Wait for table to be created
            table.wait_until_exists()
            logger.info(f"Created table {table_name}")
            
        except Exception as e:
            logger.error(f"Failed to create table {table_name}: {e}")
    
    async def insert(self, table: str, record: Dict[str, Any]) -> Dict[str, Any]:
        """Insert a record"""
        try:
            dynamodb = await self._get_async_client()
            table_obj = await dynamodb.Table(self.tables[table])
            
            # Add ID if not present
            if 'id' not in record:
                record['id'] = str(uuid.uuid4())
            
            # Add timestamps
            now = datetime.utcnow().isoformat()
            if 'created_at' not in record:
                record['created_at'] = now
            record['updated_at'] = now
            
            # Convert any nested objects to JSON strings for DynamoDB
            processed_record = self._process_record_for_dynamodb(record)
            
            await table_obj.put_item(Item=processed_record)
            return record
            
        except Exception as e:
            logger.error(f"Error inserting record into {table}: {e}")
            raise
    
    async def find_by_id(self, table: str, record_id: str) -> Optional[Dict[str, Any]]:
        """Find record by ID"""
        try:
            dynamodb = await self._get_async_client()
            table_obj = await dynamodb.Table(self.tables[table])
            
            response = await table_obj.get_item(Key={'id': record_id})
            
            if 'Item' in response:
                return self._process_record_from_dynamodb(response['Item'])
            return None
            
        except Exception as e:
            logger.error(f"Error finding record {record_id} in {table}: {e}")
            return None
    
    async def find_by_field(self, table: str, field: str, value: Any) -> Optional[Dict[str, Any]]:
        """Find first record by field value using scan (expensive - use sparingly)"""
        try:
            dynamodb = await self._get_async_client()
            table_obj = await dynamodb.Table(self.tables[table])
            
            response = await table_obj.scan(
                FilterExpression=f"#{field} = :value",
                ExpressionAttributeNames={f"#{field}": field},
                ExpressionAttributeValues={":value": value},
                Limit=1
            )
            
            if response['Items']:
                return self._process_record_from_dynamodb(response['Items'][0])
            return None
            
        except Exception as e:
            logger.error(f"Error finding record by {field}={value} in {table}: {e}")
            return None
    
    async def find_all_by_field(self, table: str, field: str, value: Any) -> List[Dict[str, Any]]:
        """Find all records by field value using scan"""
        try:
            dynamodb = await self._get_async_client()
            table_obj = await dynamodb.Table(self.tables[table])
            
            response = await table_obj.scan(
                FilterExpression=f"#{field} = :value",
                ExpressionAttributeNames={f"#{field}": field},
                ExpressionAttributeValues={":value": value}
            )
            
            items = []
            for item in response['Items']:
                items.append(self._process_record_from_dynamodb(item))
            
            return items
            
        except Exception as e:
            logger.error(f"Error finding records by {field}={value} in {table}: {e}")
            return []
    
    async def update(self, table: str, record_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a record"""
        try:
            dynamodb = await self._get_async_client()
            table_obj = await dynamodb.Table(self.tables[table])
            
            # Add updated timestamp
            updates['updated_at'] = datetime.utcnow().isoformat()
            
            # Build update expression
            update_expression = "SET "
            expression_attribute_values = {}
            expression_attribute_names = {}
            
            for key, value in updates.items():
                attr_name = f"#{key}"
                attr_value = f":{key}"
                update_expression += f"{attr_name} = {attr_value}, "
                expression_attribute_names[attr_name] = key
                expression_attribute_values[attr_value] = value
            
            update_expression = update_expression.rstrip(", ")
            
            response = await table_obj.update_item(
                Key={'id': record_id},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_attribute_names,
                ExpressionAttributeValues=expression_attribute_values,
                ReturnValues='ALL_NEW'
            )
            
            if 'Attributes' in response:
                return self._process_record_from_dynamodb(response['Attributes'])
            return None
            
        except Exception as e:
            logger.error(f"Error updating record {record_id} in {table}: {e}")
            return None
    
    async def delete(self, table: str, record_id: str) -> bool:
        """Delete a record"""
        try:
            dynamodb = await self._get_async_client()
            table_obj = await dynamodb.Table(self.tables[table])
            
            await table_obj.delete_item(Key={'id': record_id})
            return True
            
        except Exception as e:
            logger.error(f"Error deleting record {record_id} from {table}: {e}")
            return False
    
    async def read_table(self, table: str) -> List[Dict[str, Any]]:
        """Read entire table (use with caution)"""
        try:
            dynamodb = await self._get_async_client()
            table_obj = await dynamodb.Table(self.tables[table])
            
            response = await table_obj.scan()
            
            items = []
            for item in response['Items']:
                items.append(self._process_record_from_dynamodb(item))
            
            return items
            
        except Exception as e:
            logger.error(f"Error reading table {table}: {e}")
            return []
    
    def _process_record_for_dynamodb(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Process record for DynamoDB storage"""
        processed = {}
        for key, value in record.items():
            if isinstance(value, (dict, list)):
                processed[key] = json.dumps(value)
            else:
                processed[key] = value
        return processed
    
    def _process_record_from_dynamodb(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """Process record from DynamoDB retrieval"""
        processed = {}
        for key, value in record.items():
            if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                try:
                    processed[key] = json.loads(value)
                except json.JSONDecodeError:
                    processed[key] = value
            else:
                processed[key] = value
        return processed

# Global database instance
db = DynamoDBDatabase()

async def get_db():
    """Get database instance"""
    return db

def init_db():
    """Initialize database"""
    if db.dynamodb:
        db.create_tables()
        logger.info("DynamoDB tables initialized")
    else:
        logger.warning("DynamoDB not available, skipping initialization")
