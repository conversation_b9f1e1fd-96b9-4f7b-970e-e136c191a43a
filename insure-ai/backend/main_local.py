from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
import logging

from app.core.config import settings
from app.core.security import verify_token
from app.api.routes import auth, documents, forms, templates, users

# Import local components (no AWS dependencies)
from app.core.database_local import init_db
from app.core.storage_local import init_storage, storage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="FormAir - Insurance Document AI (Local)",
    description="AI-powered insurance document processing and form filling - Local development version",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount local file serving
app.mount("/local-files", StaticFiles(directory=storage.base_path), name="local-files")

# Initialize local components
@app.on_event("startup")
async def startup_event():
    """Initialize local services on startup"""
    logger.info("Initializing local development services...")
    
    # Initialize SQLite database
    init_db()
    
    # Initialize local file storage
    init_storage()
    
    logger.info("Local development services initialized successfully")

# Security
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from JWT token"""
    token = credentials.credentials
    payload = verify_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return payload

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(users.router, prefix="/api/users", tags=["users"])
app.include_router(documents.router, prefix="/api/documents", tags=["documents"])
app.include_router(forms.router, prefix="/api/forms", tags=["forms"])
app.include_router(templates.router, prefix="/api/templates", tags=["templates"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "FormAir - Insurance Document AI API (Local Development)",
        "version": "1.0.0",
        "docs": "/docs",
        "architecture": "local-development",
        "database": "sqlite",
        "storage": "local-filesystem"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy", 
        "service": "FormAir API",
        "architecture": "local-development",
        "database": "sqlite",
        "storage": "local-filesystem"
    }

@app.get("/ready")
async def readiness_check():
    """Readiness check"""
    try:
        from app.core.database_local import db
        from app.core.storage_local import storage
        
        # Check if database file exists
        db_ready = os.path.exists(db.db_path)
        
        # Check if storage directory exists
        storage_ready = os.path.exists(storage.base_path)
        
        if db_ready and storage_ready:
            return {
                "status": "ready", 
                "database": "connected", 
                "storage": "connected",
                "database_path": db.db_path,
                "storage_path": storage.base_path
            }
        else:
            raise HTTPException(
                status_code=503,
                detail={
                    "status": "not_ready",
                    "database": "connected" if db_ready else "disconnected",
                    "storage": "connected" if storage_ready else "disconnected"
                }
            )
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail={"status": "not_ready", "error": str(e)})

if __name__ == "__main__":
    uvicorn.run(
        "main_local:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # Enable reload for development
        log_level="info"
    )
