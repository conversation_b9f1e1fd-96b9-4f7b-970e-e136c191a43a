{"serviceName": "insure-ai-backend-service", "cluster": "insure-ai-cluster", "taskDefinition": "insure-ai-backend", "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-YOUR_SUBNET_1", "subnet-YOUR_SUBNET_2"], "securityGroups": ["sg-YOUR_SECURITY_GROUP"], "assignPublicIp": "ENABLED"}}, "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:YOUR_REGION:YOUR_ACCOUNT_ID:targetgroup/insure-ai-tg/YOUR_TARGET_GROUP_ID", "containerName": "insure-ai-backend", "containerPort": 8000}], "serviceRegistries": [], "enableExecuteCommand": true, "deploymentConfiguration": {"maximumPercent": 200, "minimumHealthyPercent": 50, "deploymentCircuitBreaker": {"enable": true, "rollback": true}}, "enableECSManagedTags": true, "propagateTags": "SERVICE", "tags": [{"key": "Environment", "value": "production"}, {"key": "Application", "value": "insure-ai"}]}