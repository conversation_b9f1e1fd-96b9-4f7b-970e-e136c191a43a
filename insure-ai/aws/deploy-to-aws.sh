#!/bin/bash

# InsureAI AWS Deployment Script
# Usage: ./deploy-to-aws.sh [stack-name] [region] [domain-name]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
STACK_NAME=${1:-insure-ai-stack}
AWS_REGION=${2:-us-east-1}
DOMAIN_NAME=${3:-""}
CERTIFICATE_ARN=${4:-""}

echo -e "${GREEN}🚀 Starting InsureAI AWS Deployment${NC}"
echo -e "${BLUE}Stack Name: ${STACK_NAME}${NC}"
echo -e "${BLUE}Region: ${AWS_REGION}${NC}"
echo -e "${BLUE}Domain: ${DOMAIN_NAME}${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install AWS CLI first.${NC}"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

# Check AWS credentials
echo -e "${YELLOW}🔐 Checking AWS credentials...${NC}"
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure' first.${NC}"
    exit 1
fi

AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
echo -e "${GREEN}✅ AWS Account ID: ${AWS_ACCOUNT_ID}${NC}"

# Get VPC and Subnet information
echo -e "${YELLOW}🔍 Getting VPC and Subnet information...${NC}"
VPC_ID=$(aws ec2 describe-vpcs --filters "Name=is-default,Values=true" --query 'Vpcs[0].VpcId' --output text --region $AWS_REGION)
if [ "$VPC_ID" = "None" ] || [ -z "$VPC_ID" ]; then
    echo -e "${RED}❌ No default VPC found. Please specify VPC ID manually.${NC}"
    exit 1
fi

SUBNET_IDS=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" --query 'Subnets[?MapPublicIpOnLaunch==`true`].SubnetId' --output text --region $AWS_REGION)
if [ -z "$SUBNET_IDS" ]; then
    echo -e "${RED}❌ No public subnets found in VPC $VPC_ID${NC}"
    exit 1
fi

# Convert space-separated to comma-separated
SUBNET_IDS_COMMA=$(echo $SUBNET_IDS | tr ' ' ',')

echo -e "${GREEN}✅ VPC ID: ${VPC_ID}${NC}"
echo -e "${GREEN}✅ Subnet IDs: ${SUBNET_IDS_COMMA}${NC}"

# Deploy CloudFormation stack
echo -e "${YELLOW}☁️  Deploying CloudFormation stack...${NC}"
aws cloudformation deploy \
    --template-file aws/cloudformation-template.yaml \
    --stack-name $STACK_NAME \
    --parameter-overrides \
        VpcId=$VPC_ID \
        SubnetIds=$SUBNET_IDS_COMMA \
        DomainName=$DOMAIN_NAME \
        CertificateArn=$CERTIFICATE_ARN \
    --capabilities CAPABILITY_NAMED_IAM \
    --region $AWS_REGION

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ CloudFormation stack deployed successfully${NC}"
else
    echo -e "${RED}❌ CloudFormation stack deployment failed${NC}"
    exit 1
fi

# Get stack outputs
echo -e "${YELLOW}📋 Getting stack outputs...${NC}"
ECR_URI=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query 'Stacks[0].Outputs[?OutputKey==`ECRRepositoryURI`].OutputValue' --output text --region $AWS_REGION)
ALB_DNS=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query 'Stacks[0].Outputs[?OutputKey==`LoadBalancerDNS`].OutputValue' --output text --region $AWS_REGION)
CLUSTER_NAME=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query 'Stacks[0].Outputs[?OutputKey==`ECSClusterName`].OutputValue' --output text --region $AWS_REGION)

echo -e "${GREEN}✅ ECR Repository: ${ECR_URI}${NC}"
echo -e "${GREEN}✅ Load Balancer: ${ALB_DNS}${NC}"
echo -e "${GREEN}✅ ECS Cluster: ${CLUSTER_NAME}${NC}"

# Build and push Docker image
echo -e "${YELLOW}🐳 Building and pushing Docker image...${NC}"

# Login to ECR
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_URI

# Build image
docker build -t insure-ai-backend ./backend

# Tag image
docker tag insure-ai-backend:latest $ECR_URI:latest

# Push image
docker push $ECR_URI:latest

echo -e "${GREEN}✅ Docker image pushed to ECR${NC}"

# Update ECS task definition
echo -e "${YELLOW}📝 Creating ECS task definition...${NC}"

# Replace placeholders in task definition
sed -e "s/YOUR_ACCOUNT_ID/$AWS_ACCOUNT_ID/g" \
    -e "s/YOUR_REGION/$AWS_REGION/g" \
    -e "s/YOUR_EFS_ID/$(aws efs describe-file-systems --query 'FileSystems[?Name==`insure-ai-efs`].FileSystemId' --output text --region $AWS_REGION)/g" \
    aws/ecs-task-definition.json > /tmp/ecs-task-definition.json

# Register task definition
TASK_DEFINITION_ARN=$(aws ecs register-task-definition \
    --cli-input-json file:///tmp/ecs-task-definition.json \
    --region $AWS_REGION \
    --query 'taskDefinition.taskDefinitionArn' \
    --output text)

echo -e "${GREEN}✅ Task definition registered: ${TASK_DEFINITION_ARN}${NC}"

# Create ECS service
echo -e "${YELLOW}🚀 Creating ECS service...${NC}"

# Get target group ARN
TARGET_GROUP_ARN=$(aws elbv2 describe-target-groups \
    --names insure-ai-tg \
    --query 'TargetGroups[0].TargetGroupArn' \
    --output text \
    --region $AWS_REGION)

# Get security group ID
SECURITY_GROUP_ID=$(aws ec2 describe-security-groups \
    --filters "Name=group-name,Values=*ECSSecurityGroup*" "Name=vpc-id,Values=$VPC_ID" \
    --query 'SecurityGroups[0].GroupId' \
    --output text \
    --region $AWS_REGION)

# Create service
aws ecs create-service \
    --cluster $CLUSTER_NAME \
    --service-name insure-ai-backend-service \
    --task-definition $TASK_DEFINITION_ARN \
    --desired-count 1 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_IDS_COMMA],securityGroups=[$SECURITY_GROUP_ID],assignPublicIp=ENABLED}" \
    --load-balancers "targetGroupArn=$TARGET_GROUP_ARN,containerName=insure-ai-backend,containerPort=8000" \
    --region $AWS_REGION

echo -e "${GREEN}✅ ECS service created successfully${NC}"

# Wait for service to be stable
echo -e "${YELLOW}⏳ Waiting for service to be stable...${NC}"
aws ecs wait services-stable \
    --cluster $CLUSTER_NAME \
    --services insure-ai-backend-service \
    --region $AWS_REGION

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo ""
echo -e "${GREEN}📋 Deployment Summary:${NC}"
echo -e "${BLUE}Backend URL: http://${ALB_DNS}${NC}"
echo -e "${BLUE}API Docs: http://${ALB_DNS}/docs${NC}"
echo -e "${BLUE}Health Check: http://${ALB_DNS}/health${NC}"
echo ""
echo -e "${YELLOW}🔧 Next Steps:${NC}"
echo "1. Update your Vercel frontend environment variable:"
echo "   NEXT_PUBLIC_API_URL=http://${ALB_DNS}"
echo ""
echo "2. If you have a domain name, create a CNAME record:"
echo "   api.yourdomain.com -> ${ALB_DNS}"
echo ""
echo "3. For HTTPS, update the CloudFormation stack with your certificate ARN"

# Cleanup
rm -f /tmp/ecs-task-definition.json
