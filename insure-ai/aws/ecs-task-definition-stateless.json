{"family": "insure-ai-backend-stateless", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/insure-ai-task-role", "containerDefinitions": [{"name": "insure-ai-backend-stateless", "image": "YOUR_ACCOUNT_ID.dkr.ecr.YOUR_REGION.amazonaws.com/insure-ai-backend:latest", "portMappings": [{"containerPort": 8000, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DEBUG", "value": "false"}, {"name": "AWS_REGION", "value": "us-east-1"}, {"name": "BEDROCK_MODEL_ID", "value": "anthropic.claude-3-sonnet-********-v1:0"}, {"name": "DATABASE_TYPE", "value": "dynamodb"}], "secrets": [{"name": "SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:YOUR_REGION:YOUR_ACCOUNT_ID:secret:insure-ai/secret-key"}, {"name": "ALLOWED_ORIGINS", "valueFrom": "arn:aws:secretsmanager:YOUR_REGION:YOUR_ACCOUNT_ID:secret:insure-ai/allowed-origins"}, {"name": "AWS_ACCOUNT_ID", "valueFrom": "arn:aws:secretsmanager:YOUR_REGION:YOUR_ACCOUNT_ID:secret:insure-ai/aws-account-id"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/insure-ai-backend-stateless", "awslogs-region": "YOUR_REGION", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"], "interval": 30, "timeout": 10, "retries": 3, "startPeriod": 60}}]}