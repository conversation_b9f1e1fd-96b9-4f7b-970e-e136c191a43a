# Quick Deployment Guide

## 🚀 Ready-to-Deploy Configuration Created!

Your InsureAI application now has complete deployment configurations for multiple platforms.

## Files Created:

### Docker Configuration
- ✅ `backend/Dockerfile` - Backend containerization
- ✅ `frontend/Dockerfile` - Frontend containerization  
- ✅ `docker-compose.yml` - Development environment
- ✅ `docker-compose.prod.yml` - Production environment
- ✅ `nginx.conf` - Reverse proxy configuration

### Cloud Platform Configs
- ✅ `vercel.json` - Vercel deployment config
- ✅ `railway.toml` - Railway deployment config

### Environment & Scripts
- ✅ `.env.example` - Environment variables template
- ✅ `deploy.sh` - Automated deployment script
- ✅ `DEPLOYMENT.md` - Comprehensive deployment guide

## 🎯 Quick Start Options:

### Option 1: Local Docker Deployment (Easiest)
```bash
# 1. Copy environment file
cp .env.example .env

# 2. Edit .env with your values (especially SECRET_KEY and AWS credentials)
nano .env

# 3. Make deploy script executable
chmod +x deploy.sh

# 4. Deploy
./deploy.sh development

# Access your app:
# Frontend: http://localhost:3000
# Backend: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### Option 2: Vercel + Railway (Cloud)

#### Deploy Backend to Railway:
1. Go to [railway.app](https://railway.app)
2. Connect your GitHub repo
3. Select the backend folder
4. Set environment variables in Railway dashboard
5. Deploy!

#### Deploy Frontend to Vercel:
1. Go to [vercel.com](https://vercel.com)  
2. Import your GitHub repo
3. Set root directory to `frontend`
4. Add `NEXT_PUBLIC_API_URL` environment variable
5. Deploy!

### Option 3: DigitalOcean App Platform
1. Go to DigitalOcean App Platform
2. Import your repo
3. Configure two services (backend + frontend)
4. Set environment variables
5. Deploy!

## 🔧 Environment Variables You Need:

### Backend (.env):
```bash
SECRET_KEY=your-super-secret-key-here
DEBUG=false
ALLOWED_ORIGINS=https://your-frontend-domain.com

# AWS Credentials (for production)
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_REGION=us-east-1
```

### Frontend:
```bash
NEXT_PUBLIC_API_URL=https://your-backend-domain.com
```

## 🛠 Useful Commands:

```bash
# Development
./deploy.sh development

# Production  
./deploy.sh production

# View logs
./deploy.sh logs

# Stop services
./deploy.sh stop

# Check status
./deploy.sh status
```

## 🔒 Security Checklist:

- [ ] Change SECRET_KEY from default
- [ ] Set proper ALLOWED_ORIGINS
- [ ] Configure real AWS credentials for production
- [ ] Enable HTTPS/SSL
- [ ] Update default user passwords

## 📚 Need More Details?

Check `DEPLOYMENT.md` for comprehensive deployment instructions including:
- AWS ECS deployment
- SSL/HTTPS setup
- Monitoring and maintenance
- Troubleshooting guide
- Performance optimization

## 🆘 Quick Troubleshooting:

**CORS Errors?** 
- Check ALLOWED_ORIGINS includes your frontend domain

**API Not Connecting?**
- Verify NEXT_PUBLIC_API_URL is correct
- Check backend is running and accessible

**File Upload Issues?**
- Check Docker volume mounts
- Verify file permissions

**AWS Integration Issues?**
- Verify AWS credentials are correct
- Check AWS region settings
- Ensure Bedrock access is enabled

---

Your application is now ready for deployment! 🎉

Choose your preferred deployment method and follow the steps above. The Docker option is recommended for getting started quickly.
