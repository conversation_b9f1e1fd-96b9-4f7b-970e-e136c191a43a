events {
    worker_connections 1024;
}

http {
    upstream frontend {
        server frontend:3000;
    }

    upstream backend {
        server backend:8000;
    }

    server {
        listen 80;
        server_name localhost;

        # Frontend routes
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Backend API routes
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Backend static files
        location /uploads/ {
            proxy_pass http://backend;
        }

        location /forms/ {
            proxy_pass http://backend;
        }

        location /exports/ {
            proxy_pass http://backend;
        }

        # Health check
        location /health {
            proxy_pass http://backend;
        }

        # API docs
        location /docs {
            proxy_pass http://backend;
        }

        location /redoc {
            proxy_pass http://backend;
        }
    }
}
