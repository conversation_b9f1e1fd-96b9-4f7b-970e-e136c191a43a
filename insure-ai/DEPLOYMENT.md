# Deployment Guide for InsureAI

This guide covers multiple deployment options for your FastAPI + Next.js application.

## Prerequisites

- Docker and Docker Compose installed
- Git repository access
- Environment variables configured

## Quick Start with Docker

### 1. Local Development with Docker

```bash
# Clone and navigate to the project
cd insure-ai

# Copy environment file and configure
cp .env.example .env
# Edit .env with your actual values

# Build and run with Docker Compose
docker-compose up --build

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### 2. Production Docker Deployment

```bash
# Use production compose file
docker-compose -f docker-compose.prod.yml up --build -d

# With custom environment file
docker-compose -f docker-compose.prod.yml --env-file .env.production up -d
```

## Cloud Deployment Options

### Option 1: Vercel (Frontend) + Railway (Backend)

#### Deploy Backend to Railway

1. **Create Railway Account**: Sign up at [railway.app](https://railway.app)

2. **Deploy Backend**:
   ```bash
   # Install Railway CLI
   npm install -g @railway/cli
   
   # Login and deploy
   railway login
   railway init
   railway up
   ```

3. **Set Environment Variables in Railway**:
   - `SECRET_KEY`: Generate a secure secret key
   - `AWS_ACCESS_KEY_ID`: Your AWS access key
   - `AWS_SECRET_ACCESS_KEY`: Your AWS secret key
   - `AWS_REGION`: Your AWS region
   - `ALLOWED_ORIGINS`: Your frontend domain

#### Deploy Frontend to Vercel

1. **Connect to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Set root directory to `frontend`

2. **Configure Environment Variables**:
   - `NEXT_PUBLIC_API_URL`: Your Railway backend URL

### Option 2: DigitalOcean App Platform

1. **Create App**: Go to DigitalOcean App Platform
2. **Configure Services**:
   - **Backend Service**: 
     - Source: `backend/`
     - Build Command: `pip install -r requirements.txt`
     - Run Command: `uvicorn main:app --host 0.0.0.0 --port $PORT`
   - **Frontend Service**:
     - Source: `frontend/`
     - Build Command: `npm run build`
     - Run Command: `npm start`

### Option 3: AWS ECS/Fargate

1. **Build and Push Images**:
   ```bash
   # Build images
   docker build -t insure-ai-backend ./backend
   docker build -t insure-ai-frontend ./frontend
   
   # Tag and push to ECR
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com
   docker tag insure-ai-backend:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/insure-ai-backend:latest
   docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/insure-ai-backend:latest
   ```

2. **Create ECS Task Definitions** and **Services**

### Option 4: Traditional VPS Deployment

1. **Setup Server**:
   ```bash
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sh get-docker.sh
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **Deploy Application**:
   ```bash
   # Clone repository
   git clone <your-repo-url>
   cd insure-ai
   
   # Configure environment
   cp .env.example .env
   nano .env  # Edit with production values
   
   # Deploy with production compose
   docker-compose -f docker-compose.prod.yml up -d
   ```

## Environment Configuration

### Required Environment Variables

Create a `.env` file based on `.env.example`:

```bash
# Backend
SECRET_KEY=your-super-secret-key-change-this-in-production
DEBUG=false
ALLOWED_ORIGINS=https://yourdomain.com

# AWS (for production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Frontend
NEXT_PUBLIC_API_URL=https://your-backend-domain.com
```

## SSL/HTTPS Setup

### With Nginx (included in docker-compose.prod.yml)

1. **Get SSL Certificates**:
   ```bash
   # Using Let's Encrypt
   sudo apt install certbot
   sudo certbot certonly --standalone -d yourdomain.com
   ```

2. **Update nginx.conf** with SSL configuration

### With Cloudflare

1. Set up Cloudflare for your domain
2. Enable SSL/TLS encryption
3. Configure DNS records

## Monitoring and Maintenance

### Health Checks

Both services include health check endpoints:
- Backend: `GET /health`
- Frontend: Built-in Next.js health checks

### Logs

```bash
# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

### Updates

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose -f docker-compose.prod.yml up --build -d
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Check `ALLOWED_ORIGINS` environment variable
2. **API Connection Issues**: Verify `NEXT_PUBLIC_API_URL` is correct
3. **File Upload Issues**: Check volume mounts and permissions
4. **AWS Integration**: Verify AWS credentials and region settings

### Debug Mode

```bash
# Run in debug mode
docker-compose -f docker-compose.yml up

# Check container status
docker-compose ps

# Access container shell
docker-compose exec backend bash
docker-compose exec frontend sh
```

## Security Considerations

1. **Change Default Credentials**: Update default users in production
2. **Use Strong Secret Keys**: Generate secure SECRET_KEY
3. **Enable HTTPS**: Always use SSL in production
4. **Firewall Configuration**: Restrict access to necessary ports only
5. **Regular Updates**: Keep dependencies updated
6. **AWS Security**: Use IAM roles with minimal required permissions

## Performance Optimization

1. **Enable Gzip**: Configure nginx for compression
2. **CDN**: Use CloudFlare or AWS CloudFront
3. **Database**: Consider PostgreSQL for production
4. **Caching**: Implement Redis for session storage
5. **Load Balancing**: Use multiple instances for high traffic

## Backup Strategy

1. **Data Backup**: Regular backup of uploads and data directories
2. **Database Backup**: If using PostgreSQL, set up automated backups
3. **Code Backup**: Ensure code is in version control
4. **Environment Backup**: Securely store environment configurations
