# Local Development Guide

## 🏠 Run InsureAI Completely Locally

This guide shows you how to run the entire InsureAI application locally without any AWS dependencies. Perfect for development, testing, and demos!

## 🎯 What Runs Locally

### ✅ **Local Replacements for AWS Services**
- **SQLite Database** → Replaces DynamoDB
- **Local File System** → Replaces S3 storage
- **Mock AI Services** → Replaces AWS Bedrock
- **Local File Serving** → Replaces S3 presigned URLs

### ✅ **Full Feature Compatibility**
- User authentication and management
- Document upload and processing
- Form management and field detection
- Template system
- Data extraction and export
- All API endpoints working

## 🚀 Quick Start

### Option 1: Docker Compose (Recommended)

```bash
# 1. Navigate to project directory
cd insure-ai

# 2. Start local development stack
docker-compose -f docker-compose.local.yml up --build

# 3. Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# API Docs: http://localhost:8000/docs
```

### Option 2: Manual Setup

```bash
# 1. Backend setup
cd backend

# Install dependencies
pip install -r requirements.txt

# Copy local environment
cp .env.local .env

# Run local backend
python main_local.py

# 2. Frontend setup (in new terminal)
cd frontend

# Install dependencies
npm install

# Run frontend
npm run dev
```

## 📁 **Local Data Storage**

### **Database (SQLite)**
- **File**: `backend/local_data.db`
- **Tables**: All the same tables as DynamoDB
- **Data**: Persists between restarts
- **Viewer**: Use any SQLite browser to inspect data

### **File Storage (Local Filesystem)**
```
backend/local_storage/
├── documents/     # Uploaded documents
├── forms/         # Form files
├── templates/     # Template files
├── exports/       # Generated exports
└── .metadata/     # File metadata (mimics S3 metadata)
```

## 🔧 **Configuration**

### **Environment Variables (.env.local)**
```bash
# No AWS credentials needed!
SECRET_KEY=local-development-secret-key
DATABASE_TYPE=sqlite
AWS_ACCESS_KEY_ID=local-development  # Mock value
AWS_SECRET_ACCESS_KEY=local-development  # Mock value
```

### **Default Users (Auto-created)**
```bash
# Admin user
Username: admin
Password: admin123

# Regular users
Username: user1
Password: user123

Username: demo  
Password: demo123
```

## 🧪 **Testing the Local Setup**

### **1. Health Checks**
```bash
# Backend health
curl http://localhost:8000/health

# Readiness check
curl http://localhost:8000/ready
```

### **2. Authentication Test**
```bash
# Login
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Response will include access_token
```

### **3. File Upload Test**
```bash
# Upload a document (replace TOKEN with actual token)
curl -X POST http://localhost:8000/api/documents/upload \
  -H "Authorization: Bearer TOKEN" \
  -F "file=@test.pdf"
```

### **4. Frontend Test**
1. Open http://localhost:3000
2. Login with admin/admin123
3. Upload a document
4. Process and extract data

## 📊 **Local vs AWS Comparison**

| Feature | Local Development | AWS Production |
|---------|------------------|----------------|
| **Database** | SQLite file | DynamoDB |
| **File Storage** | Local filesystem | S3 buckets |
| **AI Processing** | Mock responses | AWS Bedrock |
| **Authentication** | JWT (same) | JWT (same) |
| **API Endpoints** | Identical | Identical |
| **File Access** | Direct serving | Presigned URLs |
| **Scaling** | Single instance | Auto-scaling |
| **Cost** | $0 | $40-130/month |

## 🔄 **Data Migration**

### **Export from Local**
```bash
# Export SQLite data to JSON
sqlite3 local_data.db ".dump" > local_backup.sql

# Copy files
cp -r local_storage/ backup_storage/
```

### **Import to AWS (when ready)**
```bash
# Use the migration scripts in aws/ directory
./aws/migrate-local-to-aws.sh
```

## 🛠 **Development Workflow**

### **1. Code Changes**
- Backend changes: Auto-reload enabled
- Frontend changes: Hot reload enabled
- Database changes: Persist in SQLite file

### **2. Reset Data**
```bash
# Reset database
rm backend/local_data.db

# Reset files
rm -rf backend/local_storage/

# Restart services to recreate
docker-compose -f docker-compose.local.yml restart
```

### **3. View Data**
```bash
# View database
sqlite3 backend/local_data.db
.tables
SELECT * FROM users;

# View files
ls -la backend/local_storage/
```

## 🐛 **Troubleshooting**

### **Common Issues**

**Port already in use:**
```bash
# Kill processes on ports 3000/8000
lsof -ti:3000 | xargs kill -9
lsof -ti:8000 | xargs kill -9
```

**Database locked:**
```bash
# Stop all containers and restart
docker-compose -f docker-compose.local.yml down
docker-compose -f docker-compose.local.yml up
```

**File permissions:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER backend/local_storage/
sudo chown -R $USER:$USER backend/local_data.db
```

**Dependencies missing:**
```bash
# Rebuild containers
docker-compose -f docker-compose.local.yml build --no-cache
```

## 📈 **Performance**

### **Local Performance Benefits**
- **No network latency** - Everything runs locally
- **Fast file access** - Direct filesystem access
- **Instant startup** - No cloud service initialization
- **Offline capable** - No internet required

### **Limitations**
- **Single instance** - No horizontal scaling
- **Limited storage** - Depends on local disk space
- **No redundancy** - Single point of failure

## 🔒 **Security Notes**

### **Local Security**
- Uses same JWT authentication as production
- Passwords are properly hashed
- CORS configured for local development
- No sensitive data exposed (mock AWS credentials)

### **Production Differences**
- Local uses mock secrets (safe for development)
- Production uses AWS Secrets Manager
- Local serves files directly (development only)
- Production uses presigned URLs (secure)

## 🎉 **Benefits of Local Development**

### **For Developers**
✅ **Fast iteration** - No deployment delays
✅ **Easy debugging** - Direct access to logs and data
✅ **Cost-free** - No cloud charges
✅ **Offline work** - No internet dependency
✅ **Full control** - Complete environment control

### **For Testing**
✅ **Consistent environment** - Same setup every time
✅ **Easy reset** - Quick data cleanup
✅ **Integration testing** - Full stack testing
✅ **Demo ready** - Perfect for presentations

### **For Learning**
✅ **Understand architecture** - See how components interact
✅ **Experiment safely** - No production impact
✅ **Learn AWS concepts** - Without AWS complexity
✅ **Practice deployment** - Before going to production

## 🚀 **Next Steps**

1. **Start with local development** to understand the application
2. **Build and test features** in the local environment
3. **When ready for production**, use the AWS deployment guides
4. **Migrate data** from local to AWS when needed

The local setup gives you a complete, production-like environment without any cloud dependencies or costs! 🎯
