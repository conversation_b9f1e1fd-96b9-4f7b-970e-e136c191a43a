// User types
export interface User {
  id: string;
  username: string;
  email: string;
  is_admin: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  user: User;
}

// Document types
export enum DocumentStatus {
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

export interface Document {
  id: string;
  filename: string;
  original_filename: string;
  file_size: number;
  content_type: string;
  user_id: string;
  status: DocumentStatus;
  extracted_text?: string;
  processing_error?: string;
  file_path: string;
  created_at: string;
  updated_at: string;
}

export interface ExtractedData {
  id: string;
  document_id: string;
  template_id: string;
  user_id: string;
  extracted_values: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Form types
export enum FieldType {
  TEXT = 'text',
  NUMBER = 'number',
  DATE = 'date',
  EMAIL = 'email',
  PHONE = 'phone',
  CURRENCY = 'currency',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  SELECT = 'select'
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
  page: number;
}

export interface FormField {
  id: string;
  form_id: string;
  name: string;
  label: string;
  field_type: FieldType;
  required: boolean;
  placeholder?: string;
  options?: string[];
  bounding_box?: BoundingBox;
  order: number;
  created_at: string;
  updated_at: string;
}

export interface Form {
  id: string;
  name: string;
  description?: string;
  filename: string;
  original_filename: string;
  file_size: number;
  user_id: string;
  file_path: string;
  fields: FormField[];
  created_at: string;
  updated_at: string;
}

export interface FieldDetectionResponse {
  form_id: string;
  detected_fields: FormField[];
  confidence_scores: Record<string, number>;
  success: boolean;
  error?: string;
}

// Template types
export interface TemplateField {
  id: string;
  template_id: string;
  name: string;
  label?: string;
  field_type: FieldType;
  required: boolean;
  description?: string;
  validation_pattern?: string;
  default_value?: string;
  order: number;
  created_at: string;
  updated_at: string;
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  category?: string;
  user_id: string;
  fields: TemplateField[];
  is_system: boolean;
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiError {
  detail: string;
}

export interface ApiResponse<T> {
  data?: T;
  error?: ApiError;
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ErrorState {
  hasError: boolean;
  message?: string;
}
