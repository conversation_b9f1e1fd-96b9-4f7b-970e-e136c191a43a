import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (credentials: { username: string; password: string }) => {
    const response = await api.post('/api/auth/login', credentials);
    return response.data;
  },
  
  register: async (userData: { username: string; email: string; password: string }) => {
    const response = await api.post('/api/auth/register', userData);
    return response.data;
  },
  
  getCurrentUser: async () => {
    const response = await api.get('/api/auth/me');
    return response.data;
  },
  
  logout: async () => {
    await api.post('/api/auth/logout');
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }
};

// Documents API
export const documentsAPI = {
  upload: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/api/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
  
  getAll: async () => {
    const response = await api.get('/api/documents/');
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await api.get(`/api/documents/${id}`);
    return response.data;
  },
  
  process: async (id: string) => {
    const response = await api.post(`/api/documents/${id}/process`);
    return response.data;
  },
  
  extractData: async (documentId: string, templateId: string) => {
    const response = await api.post(`/api/documents/${documentId}/extract?template_id=${templateId}`);
    return response.data;
  },
  
  delete: async (id: string) => {
    await api.delete(`/api/documents/${id}`);
  }
};

// Forms API
export const formsAPI = {
  upload: async (file: File, name: string, description?: string) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', name);
    if (description) {
      formData.append('description', description);
    }
    
    const response = await api.post('/api/forms/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
  
  getAll: async () => {
    const response = await api.get('/api/forms/');
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await api.get(`/api/forms/${id}`);
    return response.data;
  },
  
  detectFields: async (id: string) => {
    const response = await api.post(`/api/forms/${id}/detect-fields`);
    return response.data;
  },
  
  addField: async (formId: string, fieldData: any) => {
    const response = await api.post(`/api/forms/${formId}/fields`, fieldData);
    return response.data;
  },
  
  updateField: async (fieldId: string, fieldData: any) => {
    const response = await api.put(`/api/forms/fields/${fieldId}`, fieldData);
    return response.data;
  },
  
  deleteField: async (fieldId: string) => {
    await api.delete(`/api/forms/fields/${fieldId}`);
  },
  
  delete: async (id: string) => {
    await api.delete(`/api/forms/${id}`);
  }
};

// Templates API
export const templatesAPI = {
  getAll: async () => {
    const response = await api.get('/api/templates/');
    return response.data;
  },
  
  getSystem: async () => {
    const response = await api.get('/api/templates/system');
    return response.data;
  },
  
  getById: async (id: string) => {
    const response = await api.get(`/api/templates/${id}`);
    return response.data;
  },
  
  create: async (templateData: any) => {
    const response = await api.post('/api/templates/', templateData);
    return response.data;
  },
  
  update: async (id: string, templateData: any) => {
    const response = await api.put(`/api/templates/${id}`, templateData);
    return response.data;
  },
  
  delete: async (id: string) => {
    await api.delete(`/api/templates/${id}`);
  }
};

export default api;
