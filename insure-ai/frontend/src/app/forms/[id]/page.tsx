'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { formsAPI } from '@/lib/api';
import { Form, FormField, FieldDetectionResponse } from '@/types';
import {
  ArrowLeftIcon,
  CogIcon,
  DocumentIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';

export default function FormDetailPage() {
  const params = useParams();
  const router = useRouter();
  const formId = params.id as string;

  const [form, setForm] = useState<Form | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDetecting, setIsDetecting] = useState(false);

  useEffect(() => {
    if (formId) {
      fetchForm();
    }
  }, [formId]);

  const fetchForm = async () => {
    try {
      const data = await formsAPI.getById(formId);
      setForm(data);
    } catch (error) {
      console.error('Failed to fetch form:', error);
      router.push('/forms');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDetectFields = async () => {
    if (!form) return;
    
    setIsDetecting(true);
    try {
      const result: FieldDetectionResponse = await formsAPI.detectFields(form.id);
      if (result.success) {
        // Refresh form data to get updated fields
        await fetchForm();
      } else {
        console.error('Field detection failed:', result.error);
      }
    } catch (error) {
      console.error('Failed to detect fields:', error);
    } finally {
      setIsDetecting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFieldTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      text: 'bg-blue-100 text-blue-800',
      number: 'bg-green-100 text-green-800',
      date: 'bg-purple-100 text-purple-800',
      email: 'bg-orange-100 text-orange-800',
      phone: 'bg-pink-100 text-pink-800',
      currency: 'bg-yellow-100 text-yellow-800',
      checkbox: 'bg-indigo-100 text-indigo-800',
      radio: 'bg-red-100 text-red-800',
      select: 'bg-gray-100 text-gray-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!form) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">Form not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The form you're looking for doesn't exist.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back
          </button>
          <h1 className="text-2xl font-semibold text-gray-900">
            {form.name}
          </h1>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleDetectFields}
            disabled={isDetecting}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isDetecting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <EyeIcon className="h-4 w-4 mr-2" />
            )}
            {isDetecting ? 'Detecting...' : 'Detect Fields'}
          </button>
          <button
            onClick={() => router.push(`/forms/${form.id}/edit`)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <CogIcon className="h-4 w-4 mr-2" />
            Edit Fields
          </button>
        </div>
      </div>

      {/* Form Info */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div>
              <dt className="text-sm font-medium text-gray-500">File Name</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {form.original_filename}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">File Size</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {formatFileSize(form.file_size)}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Fields Detected</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {form.fields.length} fields
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {new Date(form.created_at).toLocaleDateString()}
              </dd>
            </div>
          </div>

          {form.description && (
            <div className="mt-6">
              <dt className="text-sm font-medium text-gray-500">Description</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {form.description}
              </dd>
            </div>
          )}
        </div>
      </div>

      {/* Form Fields */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Detected Fields
          </h3>
          
          {form.fields.length === 0 ? (
            <div className="text-center py-8">
              <DocumentIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No fields detected</h3>
              <p className="mt-1 text-sm text-gray-500">
                Run field detection to automatically identify form fields.
              </p>
              <div className="mt-6">
                <button
                  onClick={handleDetectFields}
                  disabled={isDetecting}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {isDetecting ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <EyeIcon className="h-4 w-4 mr-2" />
                  )}
                  {isDetecting ? 'Detecting...' : 'Detect Fields'}
                </button>
              </div>
            </div>
          ) : (
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Field Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Label
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Required
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Position
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {form.fields.map((field) => (
                    <tr key={field.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {field.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {field.label}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getFieldTypeColor(field.field_type)}`}>
                          {field.field_type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {field.required ? (
                          <span className="text-red-600">Required</span>
                        ) : (
                          <span className="text-gray-400">Optional</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {field.bounding_box ? (
                          <span>
                            Page {field.bounding_box.page}, 
                            ({Math.round(field.bounding_box.x)}, {Math.round(field.bounding_box.y)})
                          </span>
                        ) : (
                          <span className="text-gray-400">No position</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
